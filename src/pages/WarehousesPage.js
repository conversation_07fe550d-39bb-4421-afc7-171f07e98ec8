// استيراد React Hooks والمكتبات المطلوبة
import { useState } from 'react'; // Hook لإدارة حالة المكونات
import { Plus, Search, Edit2, Trash2, Box, MapPin, Phone } from 'lucide-react'; // أيقونات من مكتبة Lucide
import DashboardLayout from '../components/layouts/DashboardLayout'; // تخطيط لوحة التحكم

// مكون صفحة عرض قائمة المخازن
export default function WarehousesPage() {
  const [warehouses, setWarehouses] = useState([
    {
      id: 1,
      code: 'W001',
      name: 'المخزن الرئيسي',
      location: 'المنطقة الصناعية',
      manager: 'عمر خالد',
      phone: '0599123456',
      capacity: 1000,
      usedCapacity: 750
    },
    {
      id: 2,
      code: 'W002',
      name: 'مخزن المواد الخام',
      location: 'شارع الصناعة',
      manager: 'محمد أحمد',
      phone: '0599789012',
      capacity: 500,
      usedCapacity: 200
    },
  ]);

  const [searchTerm, setSearchTerm] = useState('');
  const [showAddForm, setShowAddForm] = useState(false);
  const [newWarehouse, setNewWarehouse] = useState({
    code: '',
    name: '',
    location: '',
    manager: '',
    phone: '',
    capacity: 0,
    usedCapacity: 0
  });

  const handleSearch = (e) => setSearchTerm(e.target.value);

  const filteredWarehouses = warehouses.filter(warehouse =>
    warehouse.name.includes(searchTerm) ||
    warehouse.code.includes(searchTerm) ||
    warehouse.location.includes(searchTerm) ||
    warehouse.manager.includes(searchTerm)
  );

  const handleAddWarehouse = (e) => {
    e.preventDefault();
    setWarehouses([
      ...warehouses,
      { ...newWarehouse, id: warehouses.length + 1 }
    ]);
    setShowAddForm(false);
    setNewWarehouse({
      code: '',
      name: '',
      location: '',
      manager: '',
      phone: '',
      capacity: 0,
      usedCapacity: 0
    });
  };

  const getCapacityColor = (used, total) => {
    const percentage = (used / total) * 100;
    if (percentage >= 90) return 'bg-red-100 text-red-800';
    if (percentage >= 70) return 'bg-yellow-100 text-yellow-800';
    return 'bg-green-100 text-green-800';
  };

  const content = (
    <div className="p-6">
      <div className="flex justify-between items-center mb-6">
        <h1 className="text-2xl font-bold text-gray-900">المخازن</h1>
        <button
          onClick={() => setShowAddForm(true)}
          className="bg-blue-600 text-white px-4 py-2 rounded-md flex items-center gap-2 hover:bg-blue-700"
        >
          <Plus size={20} />
          إضافة مخزن جديد
        </button>
      </div>

      {/* Search Bar */}
      <div className="mb-6">
        <div className="relative">
          <Search className="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400" size={20} />
          <input
            type="text"
            placeholder="بحث عن مخزن..."
            className="w-full pl-4 pr-10 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
            value={searchTerm}
            onChange={handleSearch}
            dir="rtl"
          />
        </div>
      </div>

      {/* Warehouses Grid */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        {filteredWarehouses.map((warehouse) => (
          <div key={warehouse.id} className="bg-white rounded-lg shadow-sm hover:shadow-md transition-shadow">
            <div className="p-6">
              <div className="flex justify-between items-start mb-4">
                <div className="flex items-center justify-center w-10 h-10 rounded-full bg-blue-100 text-blue-600">
                  <Box size={20} />
                </div>
                <div className="flex space-x-2 space-x-reverse">
                  <button className="text-blue-600 hover:text-blue-900">
                    <Edit2 size={18} />
                  </button>
                  <button className="text-red-600 hover:text-red-900">
                    <Trash2 size={18} />
                  </button>
                </div>
              </div>

              <div className="text-right">
                <h3 className="text-lg font-semibold text-gray-900">{warehouse.name}</h3>
                <p className="text-sm text-gray-500 mt-1">كود المخزن: {warehouse.code}</p>

                <div className="mt-4 space-y-2">
                  <div className="flex items-center justify-end gap-2">
                    <span className="text-sm text-gray-600">{warehouse.location}</span>
                    <MapPin size={16} className="text-gray-400" />
                  </div>
                  <div className="flex items-center justify-end gap-2">
                    <span className="text-sm text-gray-600">{warehouse.phone}</span>
                    <Phone size={16} className="text-gray-400" />
                  </div>
                </div>

                <div className="mt-4 pt-4 border-t border-gray-100">
                  <div className="flex justify-between items-center">
                    <div className={`px-2 py-1 rounded-full text-sm ${getCapacityColor(warehouse.usedCapacity, warehouse.capacity)}`}>
                      {((warehouse.usedCapacity / warehouse.capacity) * 100).toFixed(1)}%
                    </div>
                    <p className="text-sm text-gray-600">
                      السعة: {warehouse.usedCapacity}/{warehouse.capacity}
                    </p>
                  </div>
                  <div className="mt-2 h-2 bg-gray-200 rounded-full overflow-hidden">
                    <div
                      className="h-full bg-blue-600 rounded-full"
                      style={{ width: `${(warehouse.usedCapacity / warehouse.capacity) * 100}%` }}
                    />
                  </div>
                </div>
              </div>
            </div>
          </div>
        ))}
      </div>

      {/* Add Warehouse Modal */}
      {showAddForm && (
        <div className="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full">
          <div className="relative top-20 mx-auto p-5 border w-96 shadow-lg rounded-md bg-white">
            <div className="mt-3">
              <h3 className="text-lg font-medium text-gray-900 mb-4 text-right">إضافة مخزن جديد</h3>
              <form onSubmit={handleAddWarehouse} className="space-y-4">
                {/* Form fields remain the same */}
                <div className="flex justify-end space-x-2 space-x-reverse">
                  <button
                    type="button"
                    onClick={() => setShowAddForm(false)}
                    className="bg-gray-200 text-gray-800 px-4 py-2 rounded-md hover:bg-gray-300"
                  >
                    إلغاء
                  </button>
                  <button
                    type="submit"
                    className="bg-blue-600 text-white px-4 py-2 rounded-md hover:bg-blue-700"
                  >
                    حفظ
                  </button>
                </div>
              </form>
            </div>
          </div>
        </div>
      )}
    </div>
  );

  return <DashboardLayout>{content}</DashboardLayout>;
}