import { useState } from 'react';
import { useNavigate } from 'react-router-dom';
import { ArrowRight, Save } from 'lucide-react';
import DashboardLayout from '../components/layouts/DashboardLayout';

export default function AddMaterialPage() {
  const navigate = useNavigate();
  const [material, setMaterial] = useState({
    code: '',
    name: '',
    section: '',
    unit: '',
    minQuantity: '',
    description: ''
  });

  // Mock data
  const sections = ['المواد الخام', 'مواد البناء', 'المعدات'];
  const units = ['قطعة', 'كيلوجرام', 'متر', 'متر مكعب', 'طن', 'لتر', 'كيس'];

  const handleSubmit = async (e) => {
    e.preventDefault();
    // TODO: Implement API call
    console.log('Saving material:', material);
    navigate('/materials');
  };

  const content = (
    <div className="p-6">
      <div className="flex items-center gap-4 mb-6">
        <button
          onClick={() => navigate('/materials')}
          className="text-gray-600 hover:text-gray-900"
        >
          <ArrowRight size={24} />
        </button>
        <h1 className="text-2xl font-bold text-gray-900">إضافة مادة جديدة</h1>
      </div>

      <form onSubmit={handleSubmit} className="max-w-2xl mx-auto">
        <div className="bg-white rounded-lg shadow-sm p-6 space-y-6">
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1 text-right">
              كود المادة
            </label>
            <input
              type="text"
              required
              className="w-full border border-gray-300 rounded-md shadow-sm p-2"
              value={material.code}
              onChange={(e) => setMaterial({ ...material, code: e.target.value })}
              dir="rtl"
            />
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1 text-right">
              اسم المادة
            </label>
            <input
              type="text"
              required
              className="w-full border border-gray-300 rounded-md shadow-sm p-2"
              value={material.name}
              onChange={(e) => setMaterial({ ...material, name: e.target.value })}
              dir="rtl"
            />
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1 text-right">
              القسم
            </label>
            <select
              required
              className="w-full border border-gray-300 rounded-md shadow-sm p-2"
              value={material.section}
              onChange={(e) => setMaterial({ ...material, section: e.target.value })}
              dir="rtl"
            >
              <option value="">اختر القسم</option>
              {sections.map(section => (
                <option key={section} value={section}>{section}</option>
              ))}
            </select>
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1 text-right">
              وحدة القياس
            </label>
            <select
              required
              className="w-full border border-gray-300 rounded-md shadow-sm p-2"
              value={material.unit}
              onChange={(e) => setMaterial({ ...material, unit: e.target.value })}
              dir="rtl"
            >
              <option value="">اختر الوحدة</option>
              {units.map(unit => (
                <option key={unit} value={unit}>{unit}</option>
              ))}
            </select>
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1 text-right">
              الحد الأدنى للكمية
            </label>
            <input
              type="number"
              required
              min="0"
              className="w-full border border-gray-300 rounded-md shadow-sm p-2"
              value={material.minQuantity}
              onChange={(e) => setMaterial({ ...material, minQuantity: e.target.value })}
              dir="rtl"
            />
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1 text-right">
              الوصف
            </label>
            <textarea
              rows={3}
              className="w-full border border-gray-300 rounded-md shadow-sm p-2"
              value={material.description}
              onChange={(e) => setMaterial({ ...material, description: e.target.value })}
              dir="rtl"
            />
          </div>

          <div className="flex justify-end gap-4 pt-4">
            <button
              type="button"
              onClick={() => navigate('/materials')}
              className="px-4 py-2 text-gray-700 bg-gray-100 hover:bg-gray-200 rounded-md"
            >
              إلغاء
            </button>
            <button
              type="submit"
              className="flex items-center gap-2 px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700"
            >
              <Save size={20} />
              حفظ
            </button>
          </div>
        </div>
      </form>
    </div>
  );

  return <DashboardLayout>{content}</DashboardLayout>;
}