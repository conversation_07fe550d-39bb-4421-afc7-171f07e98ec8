// استيراد React Hooks والمكتبات المطلوبة
import { useState } from 'react'; // Hook لإدارة حالة المكونات
import { Search, FileText, Download, Printer, ArrowDown, ArrowUp } from 'lucide-react'; // أيقونات من مكتبة Lucide
import DashboardLayout from '../components/layouts/DashboardLayout'; // تخطيط لوحة التحكم

// مكون صفحة تقرير حركة المخزون
export default function MovementReportPage() {
  const [filters, setFilters] = useState({
    warehouse: '',
    material: '',
    movementType: '',
    startDate: '',
    endDate: '',
  });

  const [movements] = useState([
    {
      id: 1,
      date: '2025-05-01',
      type: 'إدخال',
      material: 'حديد تسليح',
      materialCode: 'M001',
      warehouse: 'المخزن الرئيسي',
      quantity: 100,
      unit: 'طن',
      reference: 'PO-001',
      user: 'أحمد محمد',
      notes: 'استلام طلبية من المورد'
    },
    {
      id: 2,
      date: '2025-05-02',
      type: 'إخراج',
      material: 'حديد تسليح',
      materialCode: 'M001',
      warehouse: 'المخزن الرئيسي',
      quantity: 20,
      unit: 'طن',
      reference: 'SO-001',
      user: 'محمد علي',
      notes: 'صرف لمشروع البناء'
    },
  ]);

  const warehouses = ['المخزن الرئيسي', 'مخزن المواد الخام'];
  const materials = ['حديد تسليح', 'اسمنت', 'رمل'];
  const movementTypes = ['إدخال', 'إخراج', 'تحويل', 'جرد'];

  const handleExport = (format) => {
    console.log(`Exporting report as ${format}`);
  };

  const handlePrint = () => {
    console.log('Printing report');
  };

  const getMovementIcon = (type) => {
    switch (type) {
      case 'إدخال':
        return <ArrowDown size={20} className="text-green-500" />;
      case 'إخراج':
        return <ArrowUp size={20} className="text-red-500" />;
      default:
        return null;
    }
  };

  const content = (
    <div className="p-6">
      <div className="flex justify-between items-center mb-6">
        <h1 className="text-2xl font-bold text-gray-900">تقرير حركة المخزون</h1>
        <div className="flex gap-2">
          <button
            onClick={() => handleExport('excel')}
            className="flex items-center gap-2 px-4 py-2 bg-green-600 text-white rounded-md hover:bg-green-700"
          >
            <Download size={20} />
            تصدير Excel
          </button>
          <button
            onClick={() => handleExport('pdf')}
            className="flex items-center gap-2 px-4 py-2 bg-red-600 text-white rounded-md hover:bg-red-700"
          >
            <FileText size={20} />
            تصدير PDF
          </button>
          <button
            onClick={handlePrint}
            className="flex items-center gap-2 px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700"
          >
            <Printer size={20} />
            طباعة
          </button>
        </div>
      </div>

      {/* Filters */}
      <div className="bg-white rounded-lg shadow-sm p-6 mb-6">
        <div className="grid grid-cols-1 md:grid-cols-3 lg:grid-cols-5 gap-4">
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1 text-right">
              المخزن
            </label>
            <select
              className="w-full border border-gray-300 rounded-md shadow-sm p-2"
              value={filters.warehouse}
              onChange={(e) => setFilters({ ...filters, warehouse: e.target.value })}
              dir="rtl"
            >
              <option value="">الكل</option>
              {warehouses.map(warehouse => (
                <option key={warehouse} value={warehouse}>{warehouse}</option>
              ))}
            </select>
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1 text-right">
              المادة
            </label>
            <select
              className="w-full border border-gray-300 rounded-md shadow-sm p-2"
              value={filters.material}
              onChange={(e) => setFilters({ ...filters, material: e.target.value })}
              dir="rtl"
            >
              <option value="">الكل</option>
              {materials.map(material => (
                <option key={material} value={material}>{material}</option>
              ))}
            </select>
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1 text-right">
              نوع الحركة
            </label>
            <select
              className="w-full border border-gray-300 rounded-md shadow-sm p-2"
              value={filters.movementType}
              onChange={(e) => setFilters({ ...filters, movementType: e.target.value })}
              dir="rtl"
            >
              <option value="">الكل</option>
              {movementTypes.map(type => (
                <option key={type} value={type}>{type}</option>
              ))}
            </select>
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1 text-right">
              من تاريخ
            </label>
            <input
              type="date"
              className="w-full border border-gray-300 rounded-md shadow-sm p-2"
              value={filters.startDate}
              onChange={(e) => setFilters({ ...filters, startDate: e.target.value })}
            />
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1 text-right">
              إلى تاريخ
            </label>
            <input
              type="date"
              className="w-full border border-gray-300 rounded-md shadow-sm p-2"
              value={filters.endDate}
              onChange={(e) => setFilters({ ...filters, endDate: e.target.value })}
            />
          </div>
        </div>
      </div>

      {/* Movements Table */}
      <div className="bg-white rounded-lg shadow-sm overflow-hidden">
        <div className="overflow-x-auto">
          <table className="min-w-full divide-y divide-gray-200">
            <thead className="bg-gray-50">
              <tr>
                <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase">التاريخ</th>
                <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase">نوع الحركة</th>
                <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase">كود المادة</th>
                <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase">اسم المادة</th>
                <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase">المخزن</th>
                <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase">الكمية</th>
                <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase">الوحدة</th>
                <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase">المرجع</th>
                <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase">المستخدم</th>
                <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase">ملاحظات</th>
              </tr>
            </thead>
            <tbody className="bg-white divide-y divide-gray-200">
              {movements.map((movement) => (
                <tr key={movement.id} className="hover:bg-gray-50">
                  <td className="px-6 py-4 whitespace-nowrap text-right">{new Date(movement.date).toLocaleDateString('ar-EG')}</td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <div className="flex items-center gap-2 justify-end">
                      <span className={`${
                        movement.type === 'إدخال' ? 'text-green-700' : 'text-red-700'
                      }`}>
                        {movement.type}
                      </span>
                      {getMovementIcon(movement.type)}
                    </div>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-right">{movement.materialCode}</td>
                  <td className="px-6 py-4 whitespace-nowrap text-right">{movement.material}</td>
                  <td className="px-6 py-4 whitespace-nowrap text-right">{movement.warehouse}</td>
                  <td className="px-6 py-4 whitespace-nowrap text-right font-medium">{movement.quantity}</td>
                  <td className="px-6 py-4 whitespace-nowrap text-right">{movement.unit}</td>
                  <td className="px-6 py-4 whitespace-nowrap text-right">{movement.reference}</td>
                  <td className="px-6 py-4 whitespace-nowrap text-right">{movement.user}</td>
                  <td className="px-6 py-4 text-right">{movement.notes}</td>
                </tr>
              ))}
            </tbody>
          </table>
        </div>

        {/* Summary */}
        <div className="bg-gray-50 px-6 py-4">
          <div className="flex justify-between text-sm text-gray-700">
            <div>إجمالي الحركات: <span className="font-semibold">{movements.length}</span></div>
            <div>
              الإدخالات: <span className="font-semibold text-green-600">{movements.filter(m => m.type === 'إدخال').length}</span> |
              الإخراجات: <span className="font-semibold text-red-600">{movements.filter(m => m.type === 'إخراج').length}</span>
            </div>
          </div>
        </div>
      </div>
    </div>
  );

  return <DashboardLayout>{content}</DashboardLayout>;
}