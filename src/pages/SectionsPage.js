// استيراد React Hooks والمكتبات المطلوبة
import { useState } from 'react'; // Hook لإدارة حالة المكونات
import { Plus, Search, Edit2, Trash2, Users } from 'lucide-react'; // أيقونات من مكتبة Lucide
import DashboardLayout from '../components/layouts/DashboardLayout'; // تخطيط لوحة التحكم

// مكون صفحة عرض قائمة الأقسام
export default function SectionsPage() {
  const [sections, setSections] = useState([
    { id: 1, code: 'S001', name: 'المواد الخام', manager: 'أحمد محمد', materialsCount: 15 },
    { id: 2, code: 'S002', name: 'مواد البناء', manager: 'محمد علي', materialsCount: 25 },
    { id: 3, code: 'S003', name: 'المعدات', manager: 'خالد عمر', materialsCount: 10 },
  ]);

  const [searchTerm, setSearchTerm] = useState('');
  const [showAddForm, setShowAddForm] = useState(false);
  const [newSection, setNewSection] = useState({
    code: '',
    name: '',
    manager: '',
  });

  const handleSearch = (e) => setSearchTerm(e.target.value);

  const filteredSections = sections.filter(section =>
    section.name.includes(searchTerm) ||
    section.code.includes(searchTerm) ||
    section.manager.includes(searchTerm)
  );

  const handleAddSection = (e) => {
    e.preventDefault();
    setSections([
      ...sections,
      { ...newSection, id: sections.length + 1, materialsCount: 0 }
    ]);
    setShowAddForm(false);
    setNewSection({ code: '', name: '', manager: '' });
  };

  const content = (
    <div className="p-6">
      <div className="flex justify-between items-center mb-6">
        <h1 className="text-2xl font-bold text-gray-900">الأقسام</h1>
        <button
          onClick={() => setShowAddForm(true)}
          className="bg-blue-600 text-white px-4 py-2 rounded-md flex items-center gap-2 hover:bg-blue-700"
        >
          <Plus size={20} />
          إضافة قسم جديد
        </button>
      </div>

      {/* Search Bar */}
      <div className="mb-6">
        <div className="relative">
          <Search className="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400" size={20} />
          <input
            type="text"
            placeholder="بحث عن قسم..."
            className="w-full pl-4 pr-10 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
            value={searchTerm}
            onChange={handleSearch}
            dir="rtl"
          />
        </div>
      </div>

      {/* Sections Grid */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        {filteredSections.map((section) => (
          <div key={section.id} className="bg-white rounded-lg shadow p-6">
            <div className="flex justify-between items-start mb-4">
              <div className="flex items-center justify-center w-10 h-10 rounded-full bg-blue-100 text-blue-600">
                <Users size={20} />
              </div>
              <div className="flex space-x-2 space-x-reverse">
                <button className="text-blue-600 hover:text-blue-900">
                  <Edit2 size={18} />
                </button>
                <button className="text-red-600 hover:text-red-900">
                  <Trash2 size={18} />
                </button>
              </div>
            </div>
            <div className="text-right">
              <h3 className="text-lg font-semibold text-gray-900">{section.name}</h3>
              <p className="text-sm text-gray-500 mt-1">كود القسم: {section.code}</p>
              <p className="text-sm text-gray-500 mt-1">المدير: {section.manager}</p>
              <div className="mt-4 pt-4 border-t border-gray-100">
                <p className="text-sm font-medium text-gray-900">
                  عدد المواد: {section.materialsCount}
                </p>
              </div>
            </div>
          </div>
        ))}
      </div>

      {/* Add Section Modal */}
      {showAddForm && (
        <div className="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full">
          <div className="relative top-20 mx-auto p-5 border w-96 shadow-lg rounded-md bg-white">
            <div className="mt-3">
              <h3 className="text-lg font-medium text-gray-900 mb-4 text-right">إضافة قسم جديد</h3>
              <form onSubmit={handleAddSection} className="space-y-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700 text-right">كود القسم</label>
                  <input
                    type="text"
                    required
                    className="mt-1 block w-full border border-gray-300 rounded-md shadow-sm p-2"
                    value={newSection.code}
                    onChange={(e) => setNewSection({ ...newSection, code: e.target.value })}
                    dir="rtl"
                  />
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700 text-right">اسم القسم</label>
                  <input
                    type="text"
                    required
                    className="mt-1 block w-full border border-gray-300 rounded-md shadow-sm p-2"
                    value={newSection.name}
                    onChange={(e) => setNewSection({ ...newSection, name: e.target.value })}
                    dir="rtl"
                  />
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700 text-right">مدير القسم</label>
                  <input
                    type="text"
                    required
                    className="mt-1 block w-full border border-gray-300 rounded-md shadow-sm p-2"
                    value={newSection.manager}
                    onChange={(e) => setNewSection({ ...newSection, manager: e.target.value })}
                    dir="rtl"
                  />
                </div>
                <div className="flex justify-end space-x-2 space-x-reverse">
                  <button
                    type="button"
                    onClick={() => setShowAddForm(false)}
                    className="bg-gray-200 text-gray-800 px-4 py-2 rounded-md hover:bg-gray-300"
                  >
                    إلغاء
                  </button>
                  <button
                    type="submit"
                    className="bg-blue-600 text-white px-4 py-2 rounded-md hover:bg-blue-700"
                  >
                    حفظ
                  </button>
                </div>
              </form>
            </div>
          </div>
        </div>
      )}
    </div>
  );

  // Wrap the content with DashboardLayout
  return <DashboardLayout>{content}</DashboardLayout>;
}