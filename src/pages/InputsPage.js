// استيراد React Hooks والمكتبات المطلوبة
import { useState } from 'react'; // Hook لإدارة حالة المكونات
import { Plus, Search, Eye, FileText, Download } from 'lucide-react'; // أيقونات من مكتبة Lucide
import { useNavigate } from 'react-router-dom'; // Hook للتنقل بين الصفحات
import DashboardLayout from '../components/layouts/DashboardLayout'; // تخطيط لوحة التحكم
import { formatCurrency } from '../utils/currency'; // دالة تنسيق العملة

// مكون صفحة عرض قائمة الإدخالات المخزنية
export default function InputsPage() {
  // Hook للتنقل بين الصفحات
  const navigate = useNavigate();

  // حالة قائمة الإدخالات - بيانات وهمية للاختبار
  const [inputs] = useState([
    {
      id: 1, // معرف الإدخال
      date: '2025-05-01', // تاريخ الإدخال
      reference: 'IN-001', // رقم مرجعي للإدخال
      warehouse: 'المخزن الرئيسي', // المخزن المستقبل
      supplier: 'شركة المواد الإنشائية', // المورد
      status: 'مكتمل', // حالة الإدخال
      items: [ // قائمة المواد المستلمة
        { material: 'حديد تسليح', quantity: 100, unit: 'طن', price: 1000 },
        { material: 'اسمنت', quantity: 200, unit: 'كيس', price: 50 },
      ],
      totalAmount: 110000, // إجمالي قيمة الإدخال
      createdBy: 'أحمد محمد', // منشئ الإدخال
      notes: 'استلام طلبية شهر مايو' // ملاحظات
    },
    {
      id: 2,
      date: '2025-05-02',
      reference: 'IN-002',
      warehouse: 'مخزن المواد الخام',
      supplier: 'مؤسسة البناء الحديث',
      status: 'قيد المراجعة',
      items: [
        { material: 'رمل', quantity: 50, unit: 'متر مكعب', price: 200 },
      ],
      totalAmount: 10000,
      createdBy: 'محمد علي',
      notes: 'طلبية عاجلة'
    }
  ]);

  // حالة مصطلح البحث
  const [searchTerm, setSearchTerm] = useState('');
  // حالة الإدخال المختار للعرض في النافذة المنبثقة
  const [selectedInput, setSelectedInput] = useState(null);

  // دالة معالجة البحث
  const handleSearch = (e) => {
    setSearchTerm(e.target.value);
  };

  // دالة تحديد لون حالة الإدخال
  const getStatusColor = (status) => {
    switch (status) {
      case 'مكتمل':
        return 'bg-green-100 text-green-800'; // أخضر للمكتمل
      case 'قيد المراجعة':
        return 'bg-yellow-100 text-yellow-800'; // أصفر للمراجعة
      case 'ملغي':
        return 'bg-red-100 text-red-800'; // أحمر للملغي
      default:
        return 'bg-gray-100 text-gray-800'; // رمادي افتراضي
    }
  };

  // محتوى الصفحة الرئيسي
  const content = (
    <div className="p-6"> {/* حاوي رئيسي مع padding */}
      {/* شريط العنوان مع زر إضافة إدخال جديد */}
      <div className="flex justify-between items-center mb-6">
        <h1 className="text-2xl font-bold text-gray-900">الإدخالات المخزنية</h1>
        <button
          onClick={() => navigate('/inputs/new')} // الانتقال إلى صفحة إضافة إدخال جديد
          className="flex items-center gap-2 px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700"
        >
          <Plus size={20} /> {/* أيقونة الإضافة */}
          إنشاء إدخال جديد
        </button>
      </div>

      {/* قسم البحث والتصفية */}
      <div className="bg-white rounded-lg shadow-sm p-4 mb-6">
        <div className="flex gap-4">
          {/* حقل البحث */}
          <div className="flex-1">
            <div className="relative">
              <Search className="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400" size={20} />
              <input
                type="text" // نوع الحقل: نص
                placeholder="بحث في الإدخالات..."
                className="w-full pl-4 pr-10 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                value={searchTerm}
                onChange={handleSearch} // استدعاء دالة البحث
                dir="rtl" // اتجاه النص من اليمين لليسار
              />
            </div>
          </div>
          {/* زر التصدير */}
          <button
            onClick={() => {}} // TODO: تنفيذ وظيفة التصدير
            className="flex items-center gap-2 px-4 py-2 border border-gray-300 rounded-md hover:bg-gray-50"
          >
            <Download size={20} /> {/* أيقونة التحميل */}
            تصدير
          </button>
        </div>
      </div>

      {/* جدول الإدخالات */}
      <div className="bg-white rounded-lg shadow-sm overflow-hidden">
        <div className="overflow-x-auto"> {/* تمرير أفقي للجدول على الشاشات الصغيرة */}
          <table className="min-w-full divide-y divide-gray-200">
            {/* رأس الجدول */}
            <thead className="bg-gray-50">
              <tr>
                <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase">رقم الإدخال</th>
                <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase">التاريخ</th>
                <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase">المخزن</th>
                <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase">المورد</th>
                <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase">القيمة</th>
                <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase">الحالة</th>
                <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase">إجراءات</th>
              </tr>
            </thead>
            {/* محتوى الجدول */}
            <tbody className="bg-white divide-y divide-gray-200">
              {/* عرض كل إدخال في صف منفصل */}
              {inputs.map((input) => (
                <tr key={input.id} className="hover:bg-gray-50"> {/* تأثير hover */}
                  <td className="px-6 py-4 whitespace-nowrap text-right">{input.reference}</td>
                  <td className="px-6 py-4 whitespace-nowrap text-right">
                    {new Date(input.date).toLocaleDateString('ar-EG')} {/* تنسيق التاريخ بالعربية */}
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-right">{input.warehouse}</td>
                  <td className="px-6 py-4 whitespace-nowrap text-right">{input.supplier}</td>
                  <td className="px-6 py-4 whitespace-nowrap text-right font-medium">
                    {formatCurrency(input.totalAmount)} {/* تنسيق العملة */}
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-right">
                    {/* شارة الحالة مع لون مناسب */}
                    <span className={`px-2 py-1 rounded-full text-xs ${getStatusColor(input.status)}`}>
                      {input.status}
                    </span>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-right text-sm">
                    {/* زر عرض التفاصيل */}
                    <button
                      onClick={() => setSelectedInput(input)} // فتح النافذة المنبثقة
                      className="text-blue-600 hover:text-blue-900"
                    >
                      <Eye size={18} /> {/* أيقونة العين */}
                    </button>
                  </td>
                </tr>
              ))}
            </tbody>
          </table>
        </div>
      </div>

      {/* النافذة المنبثقة لعرض تفاصيل الإدخال */}
      {selectedInput && ( // عرض النافذة فقط إذا كان هناك إدخال مختار
        <div className="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full"> {/* خلفية شفافة */}
          <div className="relative top-20 mx-auto p-5 border w-3/4 shadow-lg rounded-md bg-white"> {/* النافذة المنبثقة */}
            {/* رأس النافذة */}
            <div className="flex justify-between items-start">
              {/* زر الإغلاق */}
              <button
                onClick={() => setSelectedInput(null)} // إغلاق النافذة
                className="text-gray-500 hover:text-gray-700"
              >
                ×
              </button>
              <h3 className="text-lg font-medium text-gray-900">تفاصيل الإدخال {selectedInput.reference}</h3>
            </div>

            <div className="mt-4">
              <div className="grid grid-cols-2 gap-4 mb-4">
                <div>
                  <p className="text-sm text-gray-600">التاريخ</p>
                  <p className="font-medium">{new Date(selectedInput.date).toLocaleDateString('ar-EG')}</p>
                </div>
                <div>
                  <p className="text-sm text-gray-600">المخزن</p>
                  <p className="font-medium">{selectedInput.warehouse}</p>
                </div>
                <div>
                  <p className="text-sm text-gray-600">المورد</p>
                  <p className="font-medium">{selectedInput.supplier}</p>
                </div>
                <div>
                  <p className="text-sm text-gray-600">منشئ الإدخال</p>
                  <p className="font-medium">{selectedInput.createdBy}</p>
                </div>
              </div>

              <div className="mt-6">
                <h4 className="text-md font-medium mb-2">المواد المستلمة</h4>
                <table className="min-w-full divide-y divide-gray-200">
                  <thead className="bg-gray-50">
                    <tr>
                      <th className="px-4 py-2 text-right text-xs font-medium text-gray-500">المادة</th>
                      <th className="px-4 py-2 text-right text-xs font-medium text-gray-500">الكمية</th>
                      <th className="px-4 py-2 text-right text-xs font-medium text-gray-500">الوحدة</th>
                      <th className="px-4 py-2 text-right text-xs font-medium text-gray-500">السعر</th>
                      <th className="px-4 py-2 text-right text-xs font-medium text-gray-500">الإجمالي</th>
                    </tr>
                  </thead>
                  <tbody>
                    {selectedInput.items.map((item, index) => (
                      <tr key={index} className="hover:bg-gray-50">
                        <td className="px-4 py-2">{item.material}</td>
                        <td className="px-4 py-2">{item.quantity}</td>
                        <td className="px-4 py-2">{item.unit}</td>
                        <td className="px-4 py-2">{formatCurrency(item.price)}</td>
                        <td className="px-4 py-2">{formatCurrency(item.quantity * item.price)}</td>
                      </tr>
                    ))}
                  </tbody>
                </table>
              </div>

              <div className="mt-6 flex justify-between items-center">
                <div className="text-sm text-gray-600">
                  ملاحظات: {selectedInput.notes}
                </div>
                <div className="text-lg font-bold">
                  الإجمالي: {formatCurrency(selectedInput.totalAmount)}
                </div>
              </div>

              <div className="mt-6 flex justify-end gap-2">
                <button
                  onClick={() => setSelectedInput(null)}
                  className="px-4 py-2 bg-gray-100 text-gray-700 rounded-md hover:bg-gray-200"
                >
                  إغلاق
                </button>
                <button
                  onClick={() => {}} // TODO: Implement print
                  className="flex items-center gap-2 px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700"
                >
                  <FileText size={18} />
                  طباعة
                </button>
              </div>
            </div>
          </div>
        </div>
      )}
    </div>
  );

  // إرجاع المحتوى ملفوفاً في تخطيط لوحة التحكم
  return <DashboardLayout>{content}</DashboardLayout>;
}