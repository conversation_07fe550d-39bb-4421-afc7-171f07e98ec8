// استيراد React Hooks والمكتبات المطلوبة
import { useState } from 'react'; // Hook لإدارة حالة المكونات
import { Save, Settings as SettingsIcon } from 'lucide-react'; // أيقونات من مكتبة Lucide
import DashboardLayout from '../components/layouts/DashboardLayout'; // تخطيط لوحة التحكم
import { CURRENCIES } from '../utils/currency'; // ثوابت العملات

// مكون صفحة الإعدادات
export default function SettingsPage() {
  const [settings, setSettings] = useState({
    general: {
      companyName: 'شركة إدارة المخازن',
      companyLogo: '',
      currency: 'ILS',
      language: 'ar',
    },
    notifications: {
      lowStockAlert: true,
      lowStockThreshold: 20,
      expiryAlert: true,
      expiryThreshold: 30,
      emailNotifications: true,
    },
    security: {
      sessionTimeout: 30,
      maxLoginAttempts: 3,
      passwordExpiry: 90,
      requireStrongPassword: true,
    },
    inventory: {
      allowNegativeStock: false,
      autoUpdateStock: true,
      requireApproval: true,
      defaultWarehouse: 'المخزن الرئيسي',
    }
  });

  const handleSave = (section, newSettings) => {
    setSettings(prev => ({
      ...prev,
      [section]: { ...prev[section], ...newSettings }
    }));
    // TODO: Save to backend
    console.log('Saving settings:', section, newSettings);
  };

  const content = (
    <div className="p-6">
      <div className="flex items-center gap-3 mb-6">
        <SettingsIcon size={32} className="text-gray-700" />
        <h1 className="text-2xl font-bold text-gray-900">إعدادات النظام</h1>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        {/* General Settings */}
        <div className="bg-white rounded-lg shadow-sm p-6">
          <h2 className="text-lg font-semibold text-gray-900 mb-4 text-right">الإعدادات العامة</h2>
          <div className="space-y-4">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1 text-right">
                اسم الشركة
              </label>
              <input
                type="text"
                className="w-full border border-gray-300 rounded-md shadow-sm p-2"
                value={settings.general.companyName}
                onChange={(e) => handleSave('general', { companyName: e.target.value })}
                dir="rtl"
              />
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1 text-right">
                العملة
              </label>
              <select
                className="w-full border border-gray-300 rounded-md shadow-sm p-2"
                value={settings.general.currency}
                onChange={(e) => handleSave('general', { currency: e.target.value })}
                dir="rtl"
              >
                {CURRENCIES.map(currency => (
                  <option key={currency.code} value={currency.code}>
                    {currency.name} ({currency.symbol})
                  </option>
                ))}
              </select>
              <p className="mt-1 text-sm text-gray-500 text-right">
                العملة المستخدمة في النظام والتقارير
              </p>
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1 text-right">
                اللغة
              </label>
              <select
                className="w-full border border-gray-300 rounded-md shadow-sm p-2"
                value={settings.general.language}
                onChange={(e) => handleSave('general', { language: e.target.value })}
                dir="rtl"
              >
                <option value="ar">العربية</option>
                <option value="en">الإنجليزية</option>
              </select>
            </div>
          </div>
        </div>

        {/* Notification Settings */}
        <div className="bg-white rounded-lg shadow-sm p-6">
          <h2 className="text-lg font-semibold text-gray-900 mb-4 text-right">إعدادات الإشعارات</h2>
          <div className="space-y-4">
            <div className="flex items-center justify-between">
              <label className="text-sm font-medium text-gray-700">تنبيه المخزون المنخفض</label>
              <input
                type="checkbox"
                checked={settings.notifications.lowStockAlert}
                onChange={(e) => handleSave('notifications', { lowStockAlert: e.target.checked })}
                className="rounded text-blue-600 focus:ring-blue-500"
              />
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1 text-right">
                نسبة تنبيه المخزون المنخفض (%)
              </label>
              <input
                type="number"
                className="w-full border border-gray-300 rounded-md shadow-sm p-2"
                value={settings.notifications.lowStockThreshold}
                onChange={(e) => handleSave('notifications', { lowStockThreshold: parseInt(e.target.value) })}
                dir="rtl"
              />
            </div>

            <div className="flex items-center justify-between">
              <label className="text-sm font-medium text-gray-700">تنبيه انتهاء الصلاحية</label>
              <input
                type="checkbox"
                checked={settings.notifications.expiryAlert}
                onChange={(e) => handleSave('notifications', { expiryAlert: e.target.checked })}
                className="rounded text-blue-600 focus:ring-blue-500"
              />
            </div>
          </div>
        </div>

        {/* Security Settings */}
        <div className="bg-white rounded-lg shadow-sm p-6">
          <h2 className="text-lg font-semibold text-gray-900 mb-4 text-right">إعدادات الأمان</h2>
          <div className="space-y-4">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1 text-right">
                مدة الجلسة (بالدقائق)
              </label>
              <input
                type="number"
                className="w-full border border-gray-300 rounded-md shadow-sm p-2"
                value={settings.security.sessionTimeout}
                onChange={(e) => handleSave('security', { sessionTimeout: parseInt(e.target.value) })}
                dir="rtl"
              />
            </div>

            <div className="flex items-center justify-between">
              <label className="text-sm font-medium text-gray-700">تفعيل كلمة المرور القوية</label>
              <input
                type="checkbox"
                checked={settings.security.requireStrongPassword}
                onChange={(e) => handleSave('security', { requireStrongPassword: e.target.checked })}
                className="rounded text-blue-600 focus:ring-blue-500"
              />
            </div>
          </div>
        </div>

        {/* Inventory Settings */}
        <div className="bg-white rounded-lg shadow-sm p-6">
          <h2 className="text-lg font-semibold text-gray-900 mb-4 text-right">إعدادات المخزون</h2>
          <div className="space-y-4">
            <div className="flex items-center justify-between">
              <label className="text-sm font-medium text-gray-700">السماح بالمخزون السالب</label>
              <input
                type="checkbox"
                checked={settings.inventory.allowNegativeStock}
                onChange={(e) => handleSave('inventory', { allowNegativeStock: e.target.checked })}
                className="rounded text-blue-600 focus:ring-blue-500"
              />
            </div>

            <div className="flex items-center justify-between">
              <label className="text-sm font-medium text-gray-700">طلب موافقة على الحركات</label>
              <input
                type="checkbox"
                checked={settings.inventory.requireApproval}
                onChange={(e) => handleSave('inventory', { requireApproval: e.target.checked })}
                className="rounded text-blue-600 focus:ring-blue-500"
              />
            </div>
          </div>
        </div>
      </div>

      {/* Save Button */}
      <div className="mt-6 flex justify-end">
        <button
          onClick={() => console.log('Saving all settings:', settings)}
          className="flex items-center gap-2 px-6 py-3 bg-blue-600 text-white rounded-md hover:bg-blue-700"
        >
          <Save size={20} />
          حفظ الإعدادات
        </button>
      </div>
    </div>
  );

  return <DashboardLayout>{content}</DashboardLayout>;
}