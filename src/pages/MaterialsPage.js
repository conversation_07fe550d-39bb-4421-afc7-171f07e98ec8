// استيراد React Hooks والمكتبات المطلوبة
import { useState } from 'react'; // Hook لإدارة حالة المكونات
import { Plus, Search, Edit2, Trash2 } from 'lucide-react'; // أيقونات من مكتبة Lucide
import { useNavigate } from 'react-router-dom'; // Hook للتنقل بين الصفحات
import DashboardLayout from '../components/layouts/DashboardLayout'; // تخطيط لوحة التحكم

// مكون صفحة عرض قائمة المواد
export default function MaterialsPage() {
  // Hook للتنقل بين الصفحات
  const navigate = useNavigate();

  // حالة قائمة المواد - بيانات وهمية للاختبار
  const [materials, setMaterials] = useState([
    { id: 1, code: 'M001', name: 'حديد تسليح', unit: 'طن', quantity: 50, section: 'مواد البناء', minQuantity: 10 },
    { id: 2, code: 'M002', name: 'اسمنت', unit: 'كيس', quantity: 200, section: 'مواد البناء', minQuantity: 50 },
    { id: 3, code: 'M003', name: 'رمل', unit: 'متر مكعب', quantity: 30, section: 'المواد الخام', minQuantity: 5 },
  ]);

  // حالة مصطلح البحث
  const [searchTerm, setSearchTerm] = useState('');

  // دالة معالجة البحث
  const handleSearch = (e) => {
    setSearchTerm(e.target.value);
  };

  // تصفية المواد حسب مصطلح البحث
  const filteredMaterials = materials.filter(material =>
    material.name.includes(searchTerm) || // البحث في اسم المادة
    material.code.includes(searchTerm) || // البحث في كود المادة
    material.section.includes(searchTerm) // البحث في القسم
  );

  // دالة حذف مادة
  const handleDelete = (id) => {
    if (window.confirm('هل أنت متأكد من حذف هذه المادة؟')) { // تأكيد الحذف
      setMaterials(materials.filter(material => material.id !== id)); // حذف المادة من القائمة
    }
  };

  return (
    <DashboardLayout> {/* تخطيط لوحة التحكم */}
      <div className="p-6"> {/* حاوي رئيسي مع padding */}
        {/* شريط العنوان مع زر إضافة مادة جديدة */}
        <div className="flex justify-between items-center mb-6">
          <h1 className="text-2xl font-bold text-gray-900">المواد</h1>
          <button
            onClick={() => navigate('/materials/add')} // الانتقال إلى صفحة إضافة مادة
            className="flex items-center gap-2 px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700"
          >
            <Plus size={20} /> {/* أيقونة الإضافة */}
            إضافة مادة جديدة
          </button>
        </div>

        {/* شريط البحث */}
        <div className="mb-6">
          <div className="relative">
            <Search className="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400" size={20} />
            <input
              type="text" // نوع الحقل: نص
              placeholder="بحث عن مادة..."
              className="w-full pl-4 pr-10 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
              value={searchTerm}
              onChange={handleSearch} // استدعاء دالة البحث
              dir="rtl" // اتجاه النص من اليمين لليسار
            />
          </div>
        </div>

        {/* جدول المواد */}
        <div className="bg-white rounded-lg shadow-sm overflow-hidden">
          <div className="overflow-x-auto"> {/* تمرير أفقي للجدول على الشاشات الصغيرة */}
            <table className="min-w-full divide-y divide-gray-200">
              {/* رأس الجدول */}
              <thead className="bg-gray-50">
                <tr>
                  <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase">الكود</th>
                  <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase">اسم المادة</th>
                  <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase">القسم</th>
                  <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase">الوحدة</th>
                  <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase">الكمية</th>
                  <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase">الحد الأدنى</th>
                  <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase">الإجراءات</th>
                </tr>
              </thead>
              {/* محتوى الجدول */}
              <tbody className="bg-white divide-y divide-gray-200">
                {/* عرض المواد المفلترة */}
                {filteredMaterials.map((material) => (
                  <tr key={material.id} className="hover:bg-gray-50"> {/* تأثير hover */}
                    <td className="px-6 py-4 whitespace-nowrap text-right">{material.code}</td>
                    <td className="px-6 py-4 whitespace-nowrap text-right">{material.name}</td>
                    <td className="px-6 py-4 whitespace-nowrap text-right">{material.section}</td>
                    <td className="px-6 py-4 whitespace-nowrap text-right">{material.unit}</td>
                    <td className="px-6 py-4 whitespace-nowrap text-right">
                      {/* شارة الكمية مع لون تحذيري إذا كانت أقل من الحد الأدنى */}
                      <span className={`px-2 py-1 rounded-full text-sm ${
                        material.quantity <= material.minQuantity ? 'bg-red-100 text-red-800' : 'bg-green-100 text-green-800'
                      }`}>
                        {material.quantity}
                      </span>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-right">{material.minQuantity}</td>
                    <td className="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                      {/* زر التعديل */}
                      <button
                        onClick={() => navigate(`/materials/edit/${material.id}`)} // الانتقال إلى صفحة التعديل
                        className="text-blue-600 hover:text-blue-900 mx-2"
                      >
                        <Edit2 size={18} /> {/* أيقونة التعديل */}
                      </button>
                      {/* زر الحذف */}
                      <button
                        onClick={() => handleDelete(material.id)} // استدعاء دالة الحذف
                        className="text-red-600 hover:text-red-900 mx-2"
                      >
                        <Trash2 size={18} /> {/* أيقونة الحذف */}
                      </button>
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>

          {/* ملخص الإحصائيات */}
          <div className="bg-gray-50 px-6 py-4">
            <div className="text-sm text-gray-700">
              إجمالي المواد: <span className="font-semibold">{materials.length}</span>
            </div>
          </div>
        </div>
      </div>
    </DashboardLayout>
  );
}