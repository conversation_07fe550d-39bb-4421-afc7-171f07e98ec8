// استيراد React Hooks والمكتبات المطلوبة
import { useState } from 'react'; // Hook لإدارة حالة المكونات
import { Search, FileText, Download, Printer } from 'lucide-react'; // أيقونات من مكتبة Lucide
import DashboardLayout from '../components/layouts/DashboardLayout'; // تخطيط لوحة التحكم
import { formatCurrency } from '../utils/currency'; // دالة تنسيق العملة

// مكون صفحة تقرير المخزون
export default function StockReportPage() {
  const [filters, setFilters] = useState({
    warehouse: '',
    section: '',
    dateRange: 'all',
    minQuantity: '',
  });

  const [stockItems] = useState([
    {
      id: 1,
      code: 'M001',
      name: 'حديد تسليح',
      warehouse: 'المخزن الرئيسي',
      section: 'مواد البناء',
      quantity: 500,
      unit: 'طن',
      minQuantity: 100,
      lastUpdate: '2025-05-01',
      value: 1250000
    },
    {
      id: 2,
      code: 'M002',
      name: 'اسمنت',
      warehouse: 'مخزن المواد الخام',
      section: 'مواد البناء',
      quantity: 1000,
      unit: 'كيس',
      minQuantity: 200,
      lastUpdate: '2025-05-01',
      value: 35000
    },
  ]);

  const warehouses = ['المخزن الرئيسي', 'مخزن المواد الخام'];
  const sections = ['مواد البناء', 'المواد الخام', 'المعدات'];

  const handleExport = (format) => {
    console.log(`Exporting report as ${format}`);
  };

  const handlePrint = () => {
    console.log('Printing report');
  };

  const content = (
    <div className="p-6">
      <div className="flex justify-between items-center mb-6">
        <h1 className="text-2xl font-bold text-gray-900">تقرير المخزون</h1>
        <div className="flex gap-2">
          <button
            onClick={() => handleExport('excel')}
            className="flex items-center gap-2 px-4 py-2 bg-green-600 text-white rounded-md hover:bg-green-700"
          >
            <Download size={20} />
            تصدير Excel
          </button>
          <button
            onClick={() => handleExport('pdf')}
            className="flex items-center gap-2 px-4 py-2 bg-red-600 text-white rounded-md hover:bg-red-700"
          >
            <FileText size={20} />
            تصدير PDF
          </button>
          <button
            onClick={handlePrint}
            className="flex items-center gap-2 px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700"
          >
            <Printer size={20} />
            طباعة
          </button>
        </div>
      </div>

      {/* Filters */}
      <div className="bg-white rounded-lg shadow-sm p-6 mb-6">
        <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1 text-right">
              المخزن
            </label>
            <select
              className="w-full border border-gray-300 rounded-md shadow-sm p-2"
              value={filters.warehouse}
              onChange={(e) => setFilters({ ...filters, warehouse: e.target.value })}
              dir="rtl"
            >
              <option value="">الكل</option>
              {warehouses.map(warehouse => (
                <option key={warehouse} value={warehouse}>{warehouse}</option>
              ))}
            </select>
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1 text-right">
              القسم
            </label>
            <select
              className="w-full border border-gray-300 rounded-md shadow-sm p-2"
              value={filters.section}
              onChange={(e) => setFilters({ ...filters, section: e.target.value })}
              dir="rtl"
            >
              <option value="">الكل</option>
              {sections.map(section => (
                <option key={section} value={section}>{section}</option>
              ))}
            </select>
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1 text-right">
              الفترة
            </label>
            <select
              className="w-full border border-gray-300 rounded-md shadow-sm p-2"
              value={filters.dateRange}
              onChange={(e) => setFilters({ ...filters, dateRange: e.target.value })}
              dir="rtl"
            >
              <option value="all">كل الفترات</option>
              <option value="today">اليوم</option>
              <option value="week">آخر أسبوع</option>
              <option value="month">آخر شهر</option>
              <option value="custom">فترة محددة</option>
            </select>
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1 text-right">
              الحد الأدنى للكمية
            </label>
            <input
              type="number"
              className="w-full border border-gray-300 rounded-md shadow-sm p-2"
              value={filters.minQuantity}
              onChange={(e) => setFilters({ ...filters, minQuantity: e.target.value })}
              dir="rtl"
            />
          </div>
        </div>
      </div>

      {/* Stock Table */}
      <div className="bg-white rounded-lg shadow-sm overflow-hidden">
        <div className="overflow-x-auto">
          <table className="min-w-full divide-y divide-gray-200">
            <thead className="bg-gray-50">
              <tr>
                <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase">الكود</th>
                <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase">اسم المادة</th>
                <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase">المخزن</th>
                <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase">القسم</th>
                <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase">الكمية</th>
                <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase">الوحدة</th>
                <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase">الحد الأدنى</th>
                <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase">القيمة</th>
                <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase">آخر تحديث</th>
              </tr>
            </thead>
            <tbody className="bg-white divide-y divide-gray-200">
              {stockItems.map((item) => (
                <tr key={item.id} className="hover:bg-gray-50">
                  <td className="px-6 py-4 whitespace-nowrap text-right">{item.code}</td>
                  <td className="px-6 py-4 whitespace-nowrap text-right">{item.name}</td>
                  <td className="px-6 py-4 whitespace-nowrap text-right">{item.warehouse}</td>
                  <td className="px-6 py-4 whitespace-nowrap text-right">{item.section}</td>
                  <td className="px-6 py-4 whitespace-nowrap text-right">
                    <span className={`px-2 py-1 rounded-full text-sm ${
                      item.quantity <= item.minQuantity ? 'bg-red-100 text-red-800' : 'bg-green-100 text-green-800'
                    }`}>
                      {item.quantity}
                    </span>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-right">{item.unit}</td>
                  <td className="px-6 py-4 whitespace-nowrap text-right">{item.minQuantity}</td>
                  <td className="px-6 py-4 whitespace-nowrap text-right">{formatCurrency(item.value)}</td>
                  <td className="px-6 py-4 whitespace-nowrap text-right">{new Date(item.lastUpdate).toLocaleDateString('ar-EG')}</td>
                </tr>
              ))}
            </tbody>
          </table>
        </div>

        {/* Summary */}
        <div className="bg-gray-50 px-6 py-4">
          <div className="flex justify-between text-sm text-gray-700">
            <div>إجمالي القيمة: <span className="font-semibold">{formatCurrency(stockItems.reduce((sum, item) => sum + item.value, 0))}</span></div>
            <div>عدد المواد: <span className="font-semibold">{stockItems.length}</span></div>
          </div>
        </div>
      </div>
    </div>
  );

  return <DashboardLayout>{content}</DashboardLayout>;
}