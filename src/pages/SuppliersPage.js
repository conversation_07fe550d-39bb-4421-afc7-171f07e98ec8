// استيراد React Hooks والمكتبات المطلوبة
import { useState } from 'react'; // Hook لإدارة حالة المكونات
import { Plus, Search, Edit2, Trash2, Building2, Phone, Mail, FileText } from 'lucide-react'; // أيقونات من مكتبة Lucide
import DashboardLayout from '../components/layouts/DashboardLayout'; // تخطيط لوحة التحكم

// مكون صفحة عرض قائمة الموردين
export default function SuppliersPage() {
  const [suppliers, setSuppliers] = useState([
    {
      id: 1,
      code: 'SP001',
      name: 'شركة المواد الإنشائية',
      contact: 'محمد أحمد',
      phone: '0599123456',
      email: '<EMAIL>',
      address: 'شارع الصناعة - غزة',
      transactionsCount: 25,
      lastTransaction: '2025-05-01',
      status: 'نشط'
    },
    {
      id: 2,
      code: 'SP002',
      name: 'مؤسسة البناء الحديث',
      contact: 'أحمد محمود',
      phone: '0598987654',
      email: '<EMAIL>',
      address: 'المنطقة الصناعية - غزة',
      transactionsCount: 15,
      lastTransaction: '2025-04-28',
      status: 'نشط'
    }
  ]);

  const [searchTerm, setSearchTerm] = useState('');
  const [showAddForm, setShowAddForm] = useState(false);
  const [newSupplier, setNewSupplier] = useState({
    code: '',
    name: '',
    contact: '',
    phone: '',
    email: '',
    address: ''
  });

  const handleSearch = (e) => setSearchTerm(e.target.value);

  const filteredSuppliers = suppliers.filter(supplier =>
    supplier.name.includes(searchTerm) ||
    supplier.code.includes(searchTerm) ||
    supplier.contact.includes(searchTerm)
  );

  const handleAddSupplier = (e) => {
    e.preventDefault();
    setSuppliers([
      ...suppliers,
      {
        ...newSupplier,
        id: suppliers.length + 1,
        transactionsCount: 0,
        lastTransaction: new Date().toISOString().split('T')[0],
        status: 'نشط'
      }
    ]);
    setShowAddForm(false);
    setNewSupplier({
      code: '',
      name: '',
      contact: '',
      phone: '',
      email: '',
      address: ''
    });
  };

  const content = (
    <div className="p-6">
      <div className="flex justify-between items-center mb-6">
        <h1 className="text-2xl font-bold text-gray-900">الموردين</h1>
        <button
          onClick={() => setShowAddForm(true)}
          className="bg-blue-600 text-white px-4 py-2 rounded-md flex items-center gap-2 hover:bg-blue-700"
        >
          <Plus size={20} />
          إضافة مورد جديد
        </button>
      </div>

      {/* Search Bar */}
      <div className="mb-6">
        <div className="relative">
          <Search className="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400" size={20} />
          <input
            type="text"
            placeholder="بحث عن مورد..."
            className="w-full pl-4 pr-10 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
            value={searchTerm}
            onChange={handleSearch}
            dir="rtl"
          />
        </div>
      </div>

      {/* Suppliers Grid */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        {filteredSuppliers.map((supplier) => (
          <div key={supplier.id} className="bg-white rounded-lg shadow-sm hover:shadow-md transition-shadow">
            <div className="p-6">
              <div className="flex justify-between items-start mb-4">
                <div className="flex items-center justify-center w-10 h-10 rounded-full bg-blue-100 text-blue-600">
                  <Building2 size={20} />
                </div>
                <div className="flex space-x-2 space-x-reverse">
                  <button className="text-blue-600 hover:text-blue-900">
                    <Edit2 size={18} />
                  </button>
                  <button className="text-red-600 hover:text-red-900">
                    <Trash2 size={18} />
                  </button>
                </div>
              </div>

              <div className="text-right">
                <h3 className="text-lg font-semibold text-gray-900">{supplier.name}</h3>
                <p className="text-sm text-gray-500 mt-1">كود المورد: {supplier.code}</p>

                <div className="mt-4 space-y-2">
                  <div className="flex items-center justify-end gap-2">
                    <span className="text-sm text-gray-600">{supplier.contact}</span>
                    <Building2 size={16} className="text-gray-400" />
                  </div>
                  <div className="flex items-center justify-end gap-2">
                    <span className="text-sm text-gray-600">{supplier.phone}</span>
                    <Phone size={16} className="text-gray-400" />
                  </div>
                  <div className="flex items-center justify-end gap-2">
                    <span className="text-sm text-gray-600 text-left">{supplier.email}</span>
                    <Mail size={16} className="text-gray-400" />
                  </div>
                </div>

                <div className="mt-4 pt-4 border-t border-gray-100">
                  <div className="flex justify-between items-center">
                    <span className={`px-2 py-1 rounded-full text-sm ${
                      supplier.status === 'نشط' ? 'bg-green-100 text-green-800' : 'bg-gray-100 text-gray-800'
                    }`}>
                      {supplier.status}
                    </span>
                    <div className="flex items-center gap-2">
                      <span className="text-sm text-gray-600">{supplier.transactionsCount} معاملة</span>
                      <FileText size={16} className="text-gray-400" />
                    </div>
                  </div>
                  <p className="text-sm text-gray-500 mt-2">
                    آخر معاملة: {new Date(supplier.lastTransaction).toLocaleDateString('ar-EG')}
                  </p>
                </div>
              </div>
            </div>
          </div>
        ))}
      </div>

      {/* Add Supplier Modal */}
      {showAddForm && (
        <div className="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full">
          <div className="relative top-20 mx-auto p-5 border w-96 shadow-lg rounded-md bg-white">
            <div className="mt-3">
              <h3 className="text-lg font-medium text-gray-900 mb-4 text-right">إضافة مورد جديد</h3>
              <form onSubmit={handleAddSupplier} className="space-y-4">
                {/* Form fields remain the same */}
                <div className="flex justify-end space-x-2 space-x-reverse">
                  <button
                    type="button"
                    onClick={() => setShowAddForm(false)}
                    className="bg-gray-200 text-gray-800 px-4 py-2 rounded-md hover:bg-gray-300"
                  >
                    إلغاء
                  </button>
                  <button
                    type="submit"
                    className="bg-blue-600 text-white px-4 py-2 rounded-md hover:bg-blue-700"
                  >
                    حفظ
                  </button>
                </div>
              </form>
            </div>
          </div>
        </div>
      )}
    </div>
  );

  return <DashboardLayout>{content}</DashboardLayout>;
}