// استيراد React Hooks والمكتبات المطلوبة
import { useState } from 'react'; // Hook لإدارة حالة المكونات
import { Plus, Search, Edit2, Trash2, User, Shield, Mail } from 'lucide-react'; // أيقونات من مكتبة Lucide
import DashboardLayout from '../components/layouts/DashboardLayout'; // تخطيط لوحة التحكم

// مكون صفحة عرض قائمة المستخدمين
export default function UsersPage() {
  const [users, setUsers] = useState([
    {
      id: 1,
      username: 'admin',
      name: 'مدير النظام',
      email: '<EMAIL>',
      role: 'مدير',
      permissions: ['إدارة المستخدمين', 'إدارة المخازن', 'إدارة المواد'],
      status: 'نشط',
      lastLogin: '2025-05-01'
    },
    {
      id: 2,
      username: 'warehouse_manager',
      name: 'مدير المخازن',
      email: '<EMAIL>',
      role: 'مشرف مخازن',
      permissions: ['إدارة المخازن', 'عرض المواد'],
      status: 'نشط',
      lastLogin: '2025-05-01'
    }
  ]);

  const [searchTerm, setSearchTerm] = useState('');
  const [showAddForm, setShowAddForm] = useState(false);
  const [newUser, setNewUser] = useState({
    username: '',
    name: '',
    email: '',
    role: '',
    password: '',
    confirmPassword: ''
  });

  const roles = ['مدير', 'مشرف مخازن', 'مشرف مواد', 'موظف'];

  const handleSearch = (e) => setSearchTerm(e.target.value);

  const filteredUsers = users.filter(user =>
    user.name.includes(searchTerm) ||
    user.username.includes(searchTerm) ||
    user.email.includes(searchTerm)
  );

  const handleAddUser = (e) => {
    e.preventDefault();
    if (newUser.password !== newUser.confirmPassword) {
      alert('كلمتا المرور غير متطابقتين');
      return;
    }
    setUsers([
      ...users,
      {
        ...newUser,
        id: users.length + 1,
        status: 'نشط',
        lastLogin: new Date().toISOString().split('T')[0],
        permissions: []
      }
    ]);
    setShowAddForm(false);
    setNewUser({
      username: '',
      name: '',
      email: '',
      role: '',
      password: '',
      confirmPassword: ''
    });
  };

  const content = (
    <div className="p-6">
      <div className="flex justify-between items-center mb-6">
        <h1 className="text-2xl font-bold text-gray-900">إدارة المستخدمين</h1>
        <button
          onClick={() => setShowAddForm(true)}
          className="bg-blue-600 text-white px-4 py-2 rounded-md flex items-center gap-2 hover:bg-blue-700"
        >
          <Plus size={20} />
          إضافة مستخدم جديد
        </button>
      </div>

      {/* Search Bar */}
      <div className="mb-6">
        <div className="relative">
          <Search className="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400" size={20} />
          <input
            type="text"
            placeholder="بحث عن مستخدم..."
            className="w-full pl-4 pr-10 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
            value={searchTerm}
            onChange={handleSearch}
            dir="rtl"
          />
        </div>
      </div>

      {/* Users Grid */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        {filteredUsers.map((user) => (
          <div key={user.id} className="bg-white rounded-lg shadow-sm hover:shadow-md transition-shadow">
            <div className="p-6">
              <div className="flex justify-between items-start mb-4">
                <div className="flex items-center justify-center w-10 h-10 rounded-full bg-blue-100 text-blue-600">
                  <User size={20} />
                </div>
                <div className="flex space-x-2 space-x-reverse">
                  <button className="text-blue-600 hover:text-blue-900">
                    <Edit2 size={18} />
                  </button>
                  <button className="text-red-600 hover:text-red-900">
                    <Trash2 size={18} />
                  </button>
                </div>
              </div>

              <div className="text-right">
                <h3 className="text-lg font-semibold text-gray-900">{user.name}</h3>
                <p className="text-sm text-gray-500 mt-1">@{user.username}</p>

                <div className="mt-4 space-y-2">
                  <div className="flex items-center justify-end gap-2">
                    <span className="text-sm text-gray-600">{user.role}</span>
                    <Shield size={16} className="text-gray-400" />
                  </div>
                  <div className="flex items-center justify-end gap-2">
                    <span className="text-sm text-gray-600">{user.email}</span>
                    <Mail size={16} className="text-gray-400" />
                  </div>
                </div>

                <div className="mt-4 pt-4 border-t border-gray-100">
                  <div className="flex justify-between items-center">
                    <span className={`px-2 py-1 rounded-full text-sm ${
                      user.status === 'نشط' ? 'bg-green-100 text-green-800' : 'bg-gray-100 text-gray-800'
                    }`}>
                      {user.status}
                    </span>
                    <span className="text-sm text-gray-500">
                      آخر دخول: {new Date(user.lastLogin).toLocaleDateString('ar-EG')}
                    </span>
                  </div>
                  <div className="mt-2">
                    <p className="text-sm font-medium text-gray-700">الصلاحيات:</p>
                    <div className="mt-1 flex flex-wrap gap-1">
                      {user.permissions.map((permission, index) => (
                        <span key={index} className="inline-block px-2 py-1 text-xs bg-blue-50 text-blue-700 rounded">
                          {permission}
                        </span>
                      ))}
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        ))}
      </div>

      {/* Add User Modal */}
      {showAddForm && (
        <div className="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full">
          <div className="relative top-20 mx-auto p-5 border w-96 shadow-lg rounded-md bg-white">
            <div className="mt-3">
              <h3 className="text-lg font-medium text-gray-900 mb-4 text-right">إضافة مستخدم جديد</h3>
              <form onSubmit={handleAddUser} className="space-y-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700 text-right">اسم المستخدم</label>
                  <input
                    type="text"
                    required
                    className="mt-1 block w-full border border-gray-300 rounded-md shadow-sm p-2"
                    value={newUser.username}
                    onChange={(e) => setNewUser({ ...newUser, username: e.target.value })}
                    dir="rtl"
                  />
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700 text-right">الاسم الكامل</label>
                  <input
                    type="text"
                    required
                    className="mt-1 block w-full border border-gray-300 rounded-md shadow-sm p-2"
                    value={newUser.name}
                    onChange={(e) => setNewUser({ ...newUser, name: e.target.value })}
                    dir="rtl"
                  />
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700 text-right">البريد الإلكتروني</label>
                  <input
                    type="email"
                    required
                    className="mt-1 block w-full border border-gray-300 rounded-md shadow-sm p-2"
                    value={newUser.email}
                    onChange={(e) => setNewUser({ ...newUser, email: e.target.value })}
                    dir="rtl"
                  />
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700 text-right">الدور</label>
                  <select
                    required
                    className="mt-1 block w-full border border-gray-300 rounded-md shadow-sm p-2"
                    value={newUser.role}
                    onChange={(e) => setNewUser({ ...newUser, role: e.target.value })}
                    dir="rtl"
                  >
                    <option value="">اختر الدور</option>
                    {roles.map((role) => (
                      <option key={role} value={role}>{role}</option>
                    ))}
                  </select>
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700 text-right">كلمة المرور</label>
                  <input
                    type="password"
                    required
                    className="mt-1 block w-full border border-gray-300 rounded-md shadow-sm p-2"
                    value={newUser.password}
                    onChange={(e) => setNewUser({ ...newUser, password: e.target.value })}
                    dir="rtl"
                  />
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700 text-right">تأكيد كلمة المرور</label>
                  <input
                    type="password"
                    required
                    className="mt-1 block w-full border border-gray-300 rounded-md shadow-sm p-2"
                    value={newUser.confirmPassword}
                    onChange={(e) => setNewUser({ ...newUser, confirmPassword: e.target.value })}
                    dir="rtl"
                  />
                </div>
                <div className="flex justify-end space-x-2 space-x-reverse">
                  <button
                    type="button"
                    onClick={() => setShowAddForm(false)}
                    className="bg-gray-200 text-gray-800 px-4 py-2 rounded-md hover:bg-gray-300"
                  >
                    إلغاء
                  </button>
                  <button
                    type="submit"
                    className="bg-blue-600 text-white px-4 py-2 rounded-md hover:bg-blue-700"
                  >
                    حفظ
                  </button>
                </div>
              </form>
            </div>
          </div>
        </div>
      )}
    </div>
  );

  return <DashboardLayout>{content}</DashboardLayout>;
}