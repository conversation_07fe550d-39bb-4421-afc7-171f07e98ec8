// استيراد React Hooks والمكتبات المطلوبة
import { useState } from 'react'; // Hook لإدارة حالة المكونات
import { useNavigate } from 'react-router-dom'; // Hook للتنقل بين الصفحات
import { Plus, Trash2, ArrowRight, Save } from 'lucide-react'; // أيقونات من مكتبة Lucide
import DashboardLayout from '../components/layouts/DashboardLayout'; // تخطيط لوحة التحكم

// مكون صفحة إضافة إخراج مخزني جديد
export default function AddOutputPage() {
  // Hook للتنقل بين الصفحات
  const navigate = useNavigate();

  // حالة الإخراج الرئيسية - تحتوي على جميع بيانات الإخراج المخزني
  const [output, setOutput] = useState({
    warehouse: '', // المخزن المختار
    requestedBy: '', // اسم الشخص الطالب للمواد
    department: '', // القسم الطالب للمواد
    date: new Date().toISOString().split('T')[0], // التاريخ الحالي بصيغة YYYY-MM-DD
    items: [], // قائمة المواد المطلوب إخراجها
    notes: '' // ملاحظات إضافية
  });

  // بيانات وهمية للاختبار - في التطبيق الحقيقي ستأتي من قاعدة البيانات
  const warehouses = ['المخزن الرئيسي', 'مخزن المواد الخام']; // قائمة المخازن المتاحة
  const departments = ['قسم المشاريع', 'قسم الصيانة', 'قسم الإنتاج']; // قائمة الأقسام في المؤسسة
  const materials = [
    { id: 1, code: 'M001', name: 'حديد تسليح', unit: 'طن', available: 80 },
    { id: 2, code: 'M002', name: 'اسمنت', unit: 'كيس', available: 150 },
    { id: 3, code: 'M003', name: 'رمل', unit: 'متر مكعب', available: 20 },
  ]; // قائمة المواد المتاحة مع الكميات المتوفرة

  // حالة المادة الجديدة التي يتم إضافتها للإخراج
  const [newItem, setNewItem] = useState({
    material: '', // معرف المادة المختارة
    quantity: '', // الكمية المطلوبة
    unit: '' // وحدة القياس (تملأ تلقائياً عند اختيار المادة)
  });

  // دالة إضافة مادة جديدة إلى قائمة الإخراج
  const handleAddItem = () => {
    // التحقق من وجود البيانات المطلوبة (المادة والكمية)
    if (!newItem.material || !newItem.quantity) return;
    const material = materials.find(m => m.id === parseInt(newItem.material));

    // التحقق من توفر الكمية المطلوبة في المخزن
    if (parseInt(newItem.quantity) > material.available) {
      alert(`الكمية المطلوبة غير متوفرة. المتوفر: ${material.available} ${material.unit}`);
      return;
    }

    // إضافة المادة إلى قائمة الإخراج
    setOutput(prev => ({
      ...prev, // الاحتفاظ بباقي بيانات الإخراج
      items: [
        ...prev.items, // الاحتفاظ بالمواد الموجودة
        {
          ...newItem, // نسخ بيانات المادة الجديدة
          materialName: material.name, // إضافة اسم المادة
          materialCode: material.code, // إضافة كود المادة
          unit: material.unit, // إضافة وحدة القياس
          available: material.available // إضافة الكمية المتوفرة للمراجعة
        }
      ]
    }));
    // إعادة تعيين نموذج المادة الجديدة
    setNewItem({ material: '', quantity: '', unit: '' });
  };

  // دالة حذف مادة من قائمة الإخراج
  const handleRemoveItem = (index) => {
    setOutput(prev => ({
      ...prev, // الاحتفاظ بباقي بيانات الإخراج
      items: prev.items.filter((_, i) => i !== index) // إزالة المادة بالفهرس المحدد
    }));
  };

  // دالة معالجة إرسال النموذج
  const handleSubmit = async (e) => {
    e.preventDefault(); // منع السلوك الافتراضي للنموذج
    // TODO: تنفيذ استدعاء API لحفظ الإخراج في قاعدة البيانات
    console.log('Submitting:', output); // طباعة بيانات الإخراج للاختبار
    navigate('/outputs'); // الانتقال إلى صفحة قائمة الإخراجات
  };

  // محتوى الصفحة الرئيسي
  const content = (
    <div className="p-6"> {/* حاوي رئيسي مع padding */}
      {/* شريط العنوان مع زر الرجوع */}
      <div className="flex items-center gap-4 mb-6">
        <button
          onClick={() => navigate('/outputs')} // الانتقال إلى صفحة قائمة الإخراجات
          className="text-gray-600 hover:text-gray-900"
        >
          <ArrowRight size={24} /> {/* أيقونة السهم للرجوع */}
        </button>
        <h1 className="text-2xl font-bold text-gray-900">إخراج مخزني جديد</h1>
      </div>

      {/* نموذج إدخال بيانات الإخراج */}
      <form onSubmit={handleSubmit} className="space-y-6">
        {/* قسم البيانات الأساسية */}
        <div className="bg-white rounded-lg shadow-sm p-6">
          <div className="grid grid-cols-1 md:grid-cols-4 gap-6"> {/* شبكة متجاوبة */}
            {/* حقل اختيار المخزن */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1 text-right">
                المخزن
              </label>
              <select
                required // حقل مطلوب
                className="w-full border border-gray-300 rounded-md shadow-sm p-2"
                value={output.warehouse}
                onChange={(e) => setOutput({ ...output, warehouse: e.target.value })}
                dir="rtl" // اتجاه النص من اليمين لليسار
              >
                <option value="">اختر المخزن</option>
                {/* عرض قائمة المخازن المتاحة */}
                {warehouses.map(warehouse => (
                  <option key={warehouse} value={warehouse}>{warehouse}</option>
                ))}
              </select>
            </div>

            {/* حقل اختيار القسم الطالب */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1 text-right">
                القسم الطالب
              </label>
              <select
                required // حقل مطلوب
                className="w-full border border-gray-300 rounded-md shadow-sm p-2"
                value={output.department}
                onChange={(e) => setOutput({ ...output, department: e.target.value })}
                dir="rtl" // اتجاه النص من اليمين لليسار
              >
                <option value="">اختر القسم</option>
                {/* عرض قائمة الأقسام المتاحة */}
                {departments.map(dept => (
                  <option key={dept} value={dept}>{dept}</option>
                ))}
              </select>
            </div>

            {/* حقل إدخال اسم الطالب */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1 text-right">
                اسم الطالب
              </label>
              <input
                type="text" // نوع الحقل: نص
                required // حقل مطلوب
                className="w-full border border-gray-300 rounded-md shadow-sm p-2"
                value={output.requestedBy}
                onChange={(e) => setOutput({ ...output, requestedBy: e.target.value })}
                dir="rtl" // اتجاه النص من اليمين لليسار
              />
            </div>

            {/* حقل إدخال التاريخ */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1 text-right">
                التاريخ
              </label>
              <input
                type="date" // نوع الحقل: تاريخ
                required // حقل مطلوب
                className="w-full border border-gray-300 rounded-md shadow-sm p-2"
                value={output.date}
                onChange={(e) => setOutput({ ...output, date: e.target.value })}
              />
            </div>
          </div>
        </div>

        {/* قسم إضافة المواد */}
        <div className="bg-white rounded-lg shadow-sm p-6">
          <h2 className="text-lg font-semibold text-gray-900 mb-4 text-right">المواد</h2>

          {/* نموذج إضافة مادة جديدة */}
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-4">
            {/* حقل اختيار المادة */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1 text-right">
                المادة
              </label>
              <select
                className="w-full border border-gray-300 rounded-md shadow-sm p-2"
                value={newItem.material}
                onChange={(e) => {
                  // البحث عن المادة المختارة وملء وحدة القياس تلقائياً
                  const material = materials.find(m => m.id === parseInt(e.target.value));
                  setNewItem({
                    ...newItem,
                    material: e.target.value,
                    unit: material ? material.unit : '' // ملء وحدة القياس تلقائياً
                  });
                }}
                dir="rtl"
              >
                <option value="">اختر المادة</option>
                {/* عرض قائمة المواد مع الكميات المتوفرة */}
                {materials.map(material => (
                  <option key={material.id} value={material.id}>
                    {material.name} (متوفر: {material.available} {material.unit})
                  </option>
                ))}
              </select>
            </div>

            {/* حقل إدخال الكمية */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1 text-right">
                الكمية
              </label>
              <input
                type="number" // نوع الحقل: رقم
                min="1" // الحد الأدنى: 1
                className="w-full border border-gray-300 rounded-md shadow-sm p-2"
                value={newItem.quantity}
                onChange={(e) => setNewItem({ ...newItem, quantity: e.target.value })}
                dir="rtl"
              />
            </div>

            {/* زر إضافة المادة */}
            <div className="flex items-end">
              <button
                type="button" // نوع الزر: عادي (ليس submit)
                onClick={handleAddItem} // استدعاء دالة إضافة المادة
                className="w-full flex items-center justify-center gap-2 px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700"
              >
                <Plus size={20} /> {/* أيقونة الإضافة */}
                إضافة
              </button>
            </div>
          </div>

          {/* جدول عرض المواد المضافة */}
          {output.items.length > 0 && ( // عرض الجدول فقط إذا كانت هناك مواد مضافة
            <div className="mt-4">
              <table className="min-w-full divide-y divide-gray-200">
                {/* رأس الجدول */}
                <thead className="bg-gray-50">
                  <tr>
                    <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase">كود المادة</th>
                    <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase">اسم المادة</th>
                    <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase">الكمية</th>
                    <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase">الوحدة</th>
                    <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase">المتوفر</th>
                    <th className="px-6 py-3"></th> {/* عمود زر الحذف */}
                  </tr>
                </thead>
                {/* محتوى الجدول */}
                <tbody className="bg-white divide-y divide-gray-200">
                  {/* عرض كل مادة في صف منفصل */}
                  {output.items.map((item, index) => (
                    <tr key={index}>
                      <td className="px-6 py-4 whitespace-nowrap text-right">{item.materialCode}</td>
                      <td className="px-6 py-4 whitespace-nowrap text-right">{item.materialName}</td>
                      <td className="px-6 py-4 whitespace-nowrap text-right">{item.quantity}</td>
                      <td className="px-6 py-4 whitespace-nowrap text-right">{item.unit}</td>
                      <td className="px-6 py-4 whitespace-nowrap text-right">{item.available}</td>
                      <td className="px-6 py-4 whitespace-nowrap text-right">
                        {/* زر حذف المادة */}
                        <button
                          type="button"
                          onClick={() => handleRemoveItem(index)} // استدعاء دالة الحذف
                          className="text-red-600 hover:text-red-900"
                        >
                          <Trash2 size={18} /> {/* أيقونة سلة المهملات */}
                        </button>
                      </td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>
          )}
        </div>

        {/* قسم الملاحظات */}
        <div className="bg-white rounded-lg shadow-sm p-6">
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1 text-right">
              ملاحظات
            </label>
            <textarea
              rows={3} // عدد الصفوف: 3
              className="w-full border border-gray-300 rounded-md shadow-sm p-2"
              value={output.notes}
              onChange={(e) => setOutput({ ...output, notes: e.target.value })}
              dir="rtl" // اتجاه النص من اليمين لليسار
            />
          </div>
        </div>

        {/* أزرار التحكم */}
        <div className="flex justify-end gap-4">
          {/* زر الإلغاء */}
          <button
            type="button" // نوع الزر: عادي (ليس submit)
            onClick={() => navigate('/outputs')} // الانتقال إلى صفحة قائمة الإخراجات
            className="px-4 py-2 text-gray-700 bg-gray-100 hover:bg-gray-200 rounded-md"
          >
            إلغاء
          </button>
          {/* زر الحفظ */}
          <button
            type="submit" // نوع الزر: إرسال النموذج
            className="flex items-center gap-2 px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700"
          >
            <Save size={20} /> {/* أيقونة الحفظ */}
            حفظ
          </button>
        </div>
      </form>
    </div>
  );

  // إرجاع المحتوى ملفوفاً في تخطيط لوحة التحكم
  return <DashboardLayout>{content}</DashboardLayout>;
}