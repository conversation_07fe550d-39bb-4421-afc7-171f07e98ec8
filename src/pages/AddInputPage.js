import { useState } from 'react'; 
import { useNavigate } from 'react-router-dom'; 
import { Plus, Trash2, ArrowRight, Save } from 'lucide-react'; 
import DashboardLayout from '../components/layouts/DashboardLayout';
import { formatCurrency } from '../utils/currency';

export default function AddInputPage() {
  const navigate = useNavigate();
  const [input, setInput] = useState({
    warehouse: '',
    supplier: '',
    date: new Date().toISOString().split('T')[0],
    items: [],
    notes: ''
  });

  const warehouses = ['المخزن الرئيسي', 'مخزن المواد الخام'];
  const suppliers = ['شركة المواد الإنشائية', 'مؤسسة البناء الحديث'];
  const materials = [
    { id: 1, code: 'M001', name: 'حديد تسليح', unit: 'طن', lastPrice: 2500 },
    { id: 2, code: 'M002', name: 'اسمنت', unit: 'كيس', lastPrice: 35 },
    { id: 3, code: 'M003', name: 'رمل', unit: 'متر مكعب', lastPrice: 150 },
  ];

  const [newItem, setNewItem] = useState({
    material: '',
    quantity: '',
    unit: '',
    price: ''
  });
  const handleAddItem = () => {  
    if (!newItem.material || !newItem.quantity || !newItem.price) return;
    
    const material = materials.find(m => m.id === parseInt(newItem.material));
    if (!material) return;

    setInput(prev => ({
      ...prev,
      items: [
        ...prev.items,
        {
          ...newItem,
          materialName: material.name,
          materialCode: material.code,
          unit: material.unit,
          total: parseFloat(newItem.quantity) * parseFloat(newItem.price)
        }
      ]
    }));
    setNewItem({ material: '', quantity: '', unit: '', price: '' });
  };

  const handleRemoveItem = (index) => {
    setInput(prev => ({
      ...prev,
      items: prev.items.filter((_, i) => i !== index)
    }));
  };

  const calculateTotal = () => {
    return input.items.reduce((sum, item) => sum + (item.quantity * item.price), 0);
  };

  const handleMaterialChange = (e) => {
    const materialId = e.target.value;
    const material = materials.find(m => m.id === parseInt(materialId));
    if (material) {
      setNewItem({
        ...newItem,
        material: materialId,
        unit: material.unit,
        price: material.lastPrice.toString()
      });
    }
  };

  const handleSubmit = async (e) => {
    e.preventDefault();
    console.log('Submitting:', input);
    navigate('/inputs');
  };

  const content = (
    <div className="p-6">
      <div className="flex items-center gap-4 mb-6">
        <button
          onClick={() => navigate('/inputs')}
          className="text-gray-600 hover:text-gray-900"
        >
          <ArrowRight size={24} />
        </button>
        <h1 className="text-2xl font-bold text-gray-900">إدخال مخزني جديد</h1>
      </div>

      <form onSubmit={handleSubmit} className="space-y-6">
        <div className="bg-white rounded-lg shadow-sm p-6">
          <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1 text-right">
                المخزن
              </label>
              <select
                required
                className="w-full border border-gray-300 rounded-md shadow-sm p-2"
                value={input.warehouse}
                onChange={(e) => setInput({ ...input, warehouse: e.target.value })}
                dir="rtl"
              >
                <option value="">اختر المخزن</option>
                {warehouses.map(warehouse => (
                  <option key={warehouse} value={warehouse}>{warehouse}</option>
                ))}
              </select>
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1 text-right">
                المورد
              </label>
              <select
                required
                className="w-full border border-gray-300 rounded-md shadow-sm p-2"
                value={input.supplier}
                onChange={(e) => setInput({ ...input, supplier: e.target.value })}
                dir="rtl"
              >
                <option value="">اختر المورد</option>
                {suppliers.map(supplier => (
                  <option key={supplier} value={supplier}>{supplier}</option>
                ))}
              </select>
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1 text-right">
                التاريخ
              </label>
              <input
                type="date"
                required
                className="w-full border border-gray-300 rounded-md shadow-sm p-2"
                value={input.date}
                onChange={(e) => setInput({ ...input, date: e.target.value })}
              />
            </div>
          </div>
        </div>

        {/* Add Items */}
        <div className="bg-white rounded-lg shadow-sm p-6">
          <h2 className="text-lg font-semibold text-gray-900 mb-4 text-right">المواد</h2>
          
          <div className="grid grid-cols-1 md:grid-cols-4 gap-4 mb-4">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1 text-right">
                المادة
              </label>
              <select
                className="w-full border border-gray-300 rounded-md shadow-sm p-2"
                value={newItem.material}
                onChange={handleMaterialChange}
                dir="rtl"
              >
                <option value="">اختر المادة</option>
                {materials.map(material => (
                  <option key={material.id} value={material.id}>
                    {material.name} - آخر سعر: {formatCurrency(material.lastPrice)}
                  </option>
                ))}
              </select>
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1 text-right">
                الكمية
              </label>
              <input
                type="number"
                min="0"
                step="0.01"
                className="w-full border border-gray-300 rounded-md shadow-sm p-2"
                value={newItem.quantity}
                onChange={(e) => setNewItem({ ...newItem, quantity: e.target.value })}
                dir="rtl"
              />
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1 text-right">
                السعر
              </label>
              <input
                type="number"
                min="0"
                step="0.01"
                className="w-full border border-gray-300 rounded-md shadow-sm p-2"
                value={newItem.price}
                onChange={(e) => setNewItem({ ...newItem, price: e.target.value })}
                dir="rtl"
              />
            </div>

            <div className="flex items-end">
              <button
                type="button"
                onClick={handleAddItem}
                className="w-full flex items-center justify-center gap-2 px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700"
              >
                <Plus size={20} />
                إضافة
              </button>
            </div>
          </div>

          {/* Items Table */}
          {input.items.length > 0 && (
            <div className="mt-4">
              <table className="min-w-full divide-y divide-gray-200">
                <thead className="bg-gray-50">
                  <tr>
                    <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase">كود المادة</th>
                    <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase">اسم المادة</th>
                    <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase">الكمية</th>
                    <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase">الوحدة</th>
                    <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase">السعر</th>
                    <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase">الإجمالي</th>
                    <th className="px-6 py-3"></th>
                  </tr>
                </thead>
                <tbody className="bg-white divide-y divide-gray-200">
                  {input.items.map((item, index) => (
                    <tr key={index}>
                      <td className="px-6 py-4 whitespace-nowrap text-right">{item.materialCode}</td>
                      <td className="px-6 py-4 whitespace-nowrap text-right">{item.materialName}</td>
                      <td className="px-6 py-4 whitespace-nowrap text-right">{item.quantity}</td>
                      <td className="px-6 py-4 whitespace-nowrap text-right">{item.unit}</td>
                      <td className="px-6 py-4 whitespace-nowrap text-right">{formatCurrency(item.price)}</td>
                      <td className="px-6 py-4 whitespace-nowrap text-right font-medium">
                        {formatCurrency(item.quantity * item.price)}
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-right">
                        <button
                          type="button"
                          onClick={() => handleRemoveItem(index)}
                          className="text-red-600 hover:text-red-900"
                        >
                          <Trash2 size={18} />
                        </button>
                      </td>
                    </tr>
                  ))}
                  <tr className="bg-gray-50">
                    <td colSpan="5" className="px-6 py-4 text-left font-medium">الإجمالي</td>
                    <td className="px-6 py-4 whitespace-nowrap text-right font-bold">
                      {formatCurrency(calculateTotal())}
                    </td>
                    <td></td>
                  </tr>
                </tbody>
              </table>
            </div>
          )}
        </div>

        {/* Notes */}
        <div className="bg-white rounded-lg shadow-sm p-6">
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1 text-right">
              ملاحظات
            </label>
            <textarea
              rows={3}
              className="w-full border border-gray-300 rounded-md shadow-sm p-2"
              value={input.notes}
              onChange={(e) => setInput({ ...input, notes: e.target.value })}
              dir="rtl"
            />
          </div>
        </div>

        {/* Actions */}
        <div className="flex justify-end gap-4">
          <button
            type="button"
            onClick={() => navigate('/inputs')}
            className="px-4 py-2 text-gray-700 bg-gray-100 hover:bg-gray-200 rounded-md"
          >
            إلغاء
          </button>
          <button
            type="submit"
            className="flex items-center gap-2 px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700"
          >
            <Save size={20} />
            حفظ
          </button>
        </div>
      </form>
    </div>
  );

  return <DashboardLayout>{content}</DashboardLayout>;
}