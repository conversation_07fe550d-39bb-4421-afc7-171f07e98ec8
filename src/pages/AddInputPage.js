// استيراد React Hooks والمكتبات المطلوبة
import { useState } from 'react'; // Hook لإدارة حالة المكونات
import { useNavigate } from 'react-router-dom'; // Hook للتنقل بين الصفحات
import { Plus, Trash2, ArrowRight, Save } from 'lucide-react'; // أيقونات من مكتبة Lucide
import DashboardLayout from '../components/layouts/DashboardLayout'; // تخطيط لوحة التحكم
import { formatCurrency } from '../utils/currency'; // دالة تنسيق العملة

// مكون صفحة إضافة إدخال مخزني جديد
export default function AddInputPage() {
  // Hook للتنقل بين الصفحات
  const navigate = useNavigate();

  // حالة الإدخال الرئيسية - تحتوي على جميع بيانات الإدخال المخزني
  const [input, setInput] = useState({
    warehouse: '', // المخزن المختار
    supplier: '', // المورد المختار
    date: new Date().toISOString().split('T')[0], // التاريخ الحالي بصيغة YYYY-MM-DD
    items: [], // قائمة المواد المضافة للإدخال
    notes: '' // ملاحظات إضافية
  });

  // بيانات وهمية للاختبار - في التطبيق الحقيقي ستأتي من قاعدة البيانات
  const warehouses = ['المخزن الرئيسي', 'مخزن المواد الخام']; // قائمة المخازن المتاحة
  const suppliers = ['شركة المواد الإنشائية', 'مؤسسة البناء الحديث']; // قائمة الموردين
  const materials = [
    { id: 1, code: 'M001', name: 'حديد تسليح', unit: 'طن', lastPrice: 2500 },
    { id: 2, code: 'M002', name: 'اسمنت', unit: 'كيس', lastPrice: 35 },
    { id: 3, code: 'M003', name: 'رمل', unit: 'متر مكعب', lastPrice: 150 },
  ]; // قائمة المواد المتاحة مع أكوادها ووحدات القياس وآخر الأسعار

  // حالة المادة الجديدة التي يتم إضافتها
  const [newItem, setNewItem] = useState({
    material: '', // معرف المادة المختارة
    quantity: '', // الكمية المطلوبة
    unit: '', // وحدة القياس (تملأ تلقائياً عند اختيار المادة)
    price: '' // السعر للوحدة الواحدة
  });
  // دالة إضافة مادة جديدة إلى قائمة المواد
  const handleAddItem = () => {
    // التحقق من وجود البيانات المطلوبة (المادة، الكمية، السعر)
    if (!newItem.material || !newItem.quantity || !newItem.price) return;

    // البحث عن المادة المختارة في قائمة المواد المتاحة
    const material = materials.find(m => m.id === parseInt(newItem.material));
    if (!material) return; // إذا لم توجد المادة، لا تفعل شيئاً

    // إضافة المادة الجديدة إلى قائمة المواد في الإدخال
    setInput(prev => ({
      ...prev, // الاحتفاظ بباقي بيانات الإدخال
      items: [
        ...prev.items, // الاحتفاظ بالمواد الموجودة
        {
          ...newItem, // نسخ بيانات المادة الجديدة
          materialName: material.name, // إضافة اسم المادة
          materialCode: material.code, // إضافة كود المادة
          unit: material.unit, // إضافة وحدة القياس
          total: parseFloat(newItem.quantity) * parseFloat(newItem.price) // حساب الإجمالي
        }
      ]
    }));
    // إعادة تعيين نموذج المادة الجديدة لإدخال مادة أخرى
    setNewItem({ material: '', quantity: '', unit: '', price: '' });
  };

  // دالة حذف مادة من قائمة المواد
  const handleRemoveItem = (index) => {
    setInput(prev => ({
      ...prev, // الاحتفاظ بباقي بيانات الإدخال
      items: prev.items.filter((_, i) => i !== index) // إزالة المادة بالفهرس المحدد
    }));
  };

  // دالة حساب إجمالي قيمة جميع المواد
  const calculateTotal = () => {
    return input.items.reduce((sum, item) => sum + (item.quantity * item.price), 0);
  };

  // دالة معالجة تغيير المادة المختارة
  const handleMaterialChange = (e) => {
    const materialId = e.target.value; // الحصول على معرف المادة المختارة
    const material = materials.find(m => m.id === parseInt(materialId)); // البحث عن المادة
    if (material) {
      // تحديث بيانات المادة الجديدة مع ملء الوحدة والسعر تلقائياً
      setNewItem({
        ...newItem, // الاحتفاظ بالبيانات الأخرى
        material: materialId, // تعيين معرف المادة
        unit: material.unit, // ملء وحدة القياس تلقائياً
        price: material.lastPrice.toString() // ملء آخر سعر تلقائياً
      });
    }
  };

  // دالة معالجة إرسال النموذج
  const handleSubmit = async (e) => {
    e.preventDefault(); // منع السلوك الافتراضي للنموذج
    // TODO: تنفيذ استدعاء API لحفظ البيانات في قاعدة البيانات
    console.log('Submitting:', input); // طباعة البيانات للاختبار
    navigate('/inputs'); // الانتقال إلى صفحة قائمة الإدخالات
  };

  // محتوى الصفحة الرئيسي
  const content = (
    <div className="p-6"> {/* حاوي رئيسي مع padding */}
      {/* شريط العنوان مع زر الرجوع */}
      <div className="flex items-center gap-4 mb-6">
        <button
          onClick={() => navigate('/inputs')} // الانتقال إلى صفحة قائمة الإدخالات
          className="text-gray-600 hover:text-gray-900"
        >
          <ArrowRight size={24} /> {/* أيقونة السهم للرجوع */}
        </button>
        <h1 className="text-2xl font-bold text-gray-900">إدخال مخزني جديد</h1>
      </div>

      {/* نموذج إدخال البيانات */}
      <form onSubmit={handleSubmit} className="space-y-6">
        {/* قسم البيانات الأساسية */}
        <div className="bg-white rounded-lg shadow-sm p-6">
          <div className="grid grid-cols-1 md:grid-cols-3 gap-6"> {/* شبكة متجاوبة */}
            {/* حقل اختيار المخزن */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1 text-right">
                المخزن
              </label>
              <select
                required // حقل مطلوب
                className="w-full border border-gray-300 rounded-md shadow-sm p-2"
                value={input.warehouse}
                onChange={(e) => setInput({ ...input, warehouse: e.target.value })}
                dir="rtl" // اتجاه النص من اليمين لليسار
              >
                <option value="">اختر المخزن</option>
                {/* عرض قائمة المخازن المتاحة */}
                {warehouses.map(warehouse => (
                  <option key={warehouse} value={warehouse}>{warehouse}</option>
                ))}
              </select>
            </div>

            {/* حقل اختيار المورد */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1 text-right">
                المورد
              </label>
              <select
                required // حقل مطلوب
                className="w-full border border-gray-300 rounded-md shadow-sm p-2"
                value={input.supplier}
                onChange={(e) => setInput({ ...input, supplier: e.target.value })}
                dir="rtl" // اتجاه النص من اليمين لليسار
              >
                <option value="">اختر المورد</option>
                {/* عرض قائمة الموردين المتاحين */}
                {suppliers.map(supplier => (
                  <option key={supplier} value={supplier}>{supplier}</option>
                ))}
              </select>
            </div>

            {/* حقل إدخال التاريخ */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1 text-right">
                التاريخ
              </label>
              <input
                type="date" // نوع الحقل: تاريخ
                required // حقل مطلوب
                className="w-full border border-gray-300 rounded-md shadow-sm p-2"
                value={input.date}
                onChange={(e) => setInput({ ...input, date: e.target.value })}
              />
            </div>
          </div>
        </div>

        {/* قسم إضافة المواد */}
        <div className="bg-white rounded-lg shadow-sm p-6">
          <h2 className="text-lg font-semibold text-gray-900 mb-4 text-right">المواد</h2>

          {/* نموذج إضافة مادة جديدة */}
          <div className="grid grid-cols-1 md:grid-cols-4 gap-4 mb-4">
            {/* حقل اختيار المادة */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1 text-right">
                المادة
              </label>
              <select
                className="w-full border border-gray-300 rounded-md shadow-sm p-2"
                value={newItem.material}
                onChange={handleMaterialChange} // استدعاء دالة تغيير المادة
                dir="rtl"
              >
                <option value="">اختر المادة</option>
                {/* عرض قائمة المواد مع آخر الأسعار */}
                {materials.map(material => (
                  <option key={material.id} value={material.id}>
                    {material.name} - آخر سعر: {formatCurrency(material.lastPrice)}
                  </option>
                ))}
              </select>
            </div>

            {/* حقل إدخال الكمية */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1 text-right">
                الكمية
              </label>
              <input
                type="number" // نوع الحقل: رقم
                min="0" // الحد الأدنى: صفر
                step="0.01" // خطوة الزيادة: 0.01
                className="w-full border border-gray-300 rounded-md shadow-sm p-2"
                value={newItem.quantity}
                onChange={(e) => setNewItem({ ...newItem, quantity: e.target.value })}
                dir="rtl"
              />
            </div>

            {/* حقل إدخال السعر */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1 text-right">
                السعر
              </label>
              <input
                type="number" // نوع الحقل: رقم
                min="0" // الحد الأدنى: صفر
                step="0.01" // خطوة الزيادة: 0.01
                className="w-full border border-gray-300 rounded-md shadow-sm p-2"
                value={newItem.price}
                onChange={(e) => setNewItem({ ...newItem, price: e.target.value })}
                dir="rtl"
              />
            </div>

            {/* زر إضافة المادة */}
            <div className="flex items-end">
              <button
                type="button" // نوع الزر: عادي (ليس submit)
                onClick={handleAddItem} // استدعاء دالة إضافة المادة
                className="w-full flex items-center justify-center gap-2 px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700"
              >
                <Plus size={20} /> {/* أيقونة الإضافة */}
                إضافة
              </button>
            </div>
          </div>

          {/* جدول عرض المواد المضافة */}
          {input.items.length > 0 && ( // عرض الجدول فقط إذا كانت هناك مواد مضافة
            <div className="mt-4">
              <table className="min-w-full divide-y divide-gray-200">
                {/* رأس الجدول */}
                <thead className="bg-gray-50">
                  <tr>
                    <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase">كود المادة</th>
                    <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase">اسم المادة</th>
                    <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase">الكمية</th>
                    <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase">الوحدة</th>
                    <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase">السعر</th>
                    <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase">الإجمالي</th>
                    <th className="px-6 py-3"></th> {/* عمود زر الحذف */}
                  </tr>
                </thead>
                {/* محتوى الجدول */}
                <tbody className="bg-white divide-y divide-gray-200">
                  {/* عرض كل مادة في صف منفصل */}
                  {input.items.map((item, index) => (
                    <tr key={index}>
                      <td className="px-6 py-4 whitespace-nowrap text-right">{item.materialCode}</td>
                      <td className="px-6 py-4 whitespace-nowrap text-right">{item.materialName}</td>
                      <td className="px-6 py-4 whitespace-nowrap text-right">{item.quantity}</td>
                      <td className="px-6 py-4 whitespace-nowrap text-right">{item.unit}</td>
                      <td className="px-6 py-4 whitespace-nowrap text-right">{formatCurrency(item.price)}</td>
                      <td className="px-6 py-4 whitespace-nowrap text-right font-medium">
                        {formatCurrency(item.quantity * item.price)} {/* حساب إجمالي المادة */}
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-right">
                        {/* زر حذف المادة */}
                        <button
                          type="button"
                          onClick={() => handleRemoveItem(index)} // استدعاء دالة الحذف
                          className="text-red-600 hover:text-red-900"
                        >
                          <Trash2 size={18} /> {/* أيقونة سلة المهملات */}
                        </button>
                      </td>
                    </tr>
                  ))}
                  {/* صف الإجمالي العام */}
                  <tr className="bg-gray-50">
                    <td colSpan="5" className="px-6 py-4 text-left font-medium">الإجمالي</td>
                    <td className="px-6 py-4 whitespace-nowrap text-right font-bold">
                      {formatCurrency(calculateTotal())} {/* حساب الإجمالي العام */}
                    </td>
                    <td></td>
                  </tr>
                </tbody>
              </table>
            </div>
          )}
        </div>

        {/* قسم الملاحظات */}
        <div className="bg-white rounded-lg shadow-sm p-6">
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1 text-right">
              ملاحظات
            </label>
            <textarea
              rows={3} // عدد الصفوف: 3
              className="w-full border border-gray-300 rounded-md shadow-sm p-2"
              value={input.notes}
              onChange={(e) => setInput({ ...input, notes: e.target.value })}
              dir="rtl" // اتجاه النص من اليمين لليسار
            />
          </div>
        </div>

        {/* أزرار التحكم */}
        <div className="flex justify-end gap-4">
          {/* زر الإلغاء */}
          <button
            type="button" // نوع الزر: عادي (ليس submit)
            onClick={() => navigate('/inputs')} // الانتقال إلى صفحة قائمة الإدخالات
            className="px-4 py-2 text-gray-700 bg-gray-100 hover:bg-gray-200 rounded-md"
          >
            إلغاء
          </button>
          {/* زر الحفظ */}
          <button
            type="submit" // نوع الزر: إرسال النموذج
            className="flex items-center gap-2 px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700"
          >
            <Save size={20} /> {/* أيقونة الحفظ */}
            حفظ
          </button>
        </div>
      </form>
    </div>
  );

  // إرجاع المحتوى ملفوفاً في تخطيط لوحة التحكم
  return <DashboardLayout>{content}</DashboardLayout>;
}