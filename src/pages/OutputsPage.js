// استيراد React Hooks والمكتبات المطلوبة
import { useState } from 'react'; // Hook لإدارة حالة المكونات
import { Plus, Search, Eye, FileText, Download } from 'lucide-react'; // أيقونات من مكتبة Lucide
import { useNavigate } from 'react-router-dom'; // Hook للتنقل بين الصفحات
import DashboardLayout from '../components/layouts/DashboardLayout'; // تخطيط لوحة التحكم

// مكون صفحة عرض قائمة الإخراجات المخزنية
export default function OutputsPage() {
  // Hook للتنقل بين الصفحات
  const navigate = useNavigate();

  // حالة قائمة الإخراجات - بيانات وهمية للاختبار
  const [outputs] = useState([
    {
      id: 1, // معرف الإخراج
      date: '2025-05-01', // تاريخ الإخراج
      reference: 'OUT-001', // رقم مرجعي للإخراج
      warehouse: 'المخزن الرئيسي', // المخزن المصدر
      requestedBy: 'مشروع البناء الجديد', // الجهة الطالبة
      department: 'قسم المشاريع', // القسم الطالب
      status: 'مكتمل', // حالة الإخراج
      items: [ // قائمة المواد المصروفة
        { material: 'حديد تسليح', quantity: 20, unit: 'طن' },
        { material: 'اسمنت', quantity: 50, unit: 'كيس' },
      ],
      totalItems: 2, // إجمالي عدد المواد
      createdBy: 'أحمد محمد', // منشئ الإخراج
      notes: 'صرف مواد للمشروع الجديد' // ملاحظات
    },
    {
      id: 2,
      date: '2025-05-02',
      reference: 'OUT-002',
      warehouse: 'مخزن المواد الخام',
      requestedBy: 'قسم الصيانة',
      department: 'قسم الصيانة',
      status: 'قيد المراجعة',
      items: [
        { material: 'رمل', quantity: 10, unit: 'متر مكعب' },
      ],
      totalItems: 1,
      createdBy: 'محمد علي',
      notes: 'صرف مواد للصيانة الدورية'
    }
  ]);

  // حالة مصطلح البحث
  const [searchTerm, setSearchTerm] = useState('');
  // حالة الإخراج المختار للعرض في النافذة المنبثقة
  const [selectedOutput, setSelectedOutput] = useState(null);

  // دالة معالجة البحث
  const handleSearch = (e) => {
    setSearchTerm(e.target.value);
  };

  // دالة تحديد لون حالة الإخراج
  const getStatusColor = (status) => {
    switch (status) {
      case 'مكتمل':
        return 'bg-green-100 text-green-800'; // أخضر للمكتمل
      case 'قيد المراجعة':
        return 'bg-yellow-100 text-yellow-800'; // أصفر للمراجعة
      case 'ملغي':
        return 'bg-red-100 text-red-800'; // أحمر للملغي
      default:
        return 'bg-gray-100 text-gray-800'; // رمادي افتراضي
    }
  };

  const content = (
    <div className="p-6">
      <div className="flex justify-between items-center mb-6">
        <h1 className="text-2xl font-bold text-gray-900">الإخراجات المخزنية</h1>
        <button
          onClick={() => navigate('/outputs/new')}
          className="flex items-center gap-2 px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700"
        >
          <Plus size={20} />
          إنشاء إخراج جديد
        </button>
      </div>

      {/* Search and Filter */}
      <div className="bg-white rounded-lg shadow-sm p-4 mb-6">
        <div className="flex gap-4">
          <div className="flex-1">
            <div className="relative">
              <Search className="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400" size={20} />
              <input
                type="text"
                placeholder="بحث في الإخراجات..."
                className="w-full pl-4 pr-10 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                value={searchTerm}
                onChange={handleSearch}
                dir="rtl"
              />
            </div>
          </div>
          <button
            onClick={() => {}} // TODO: Implement export
            className="flex items-center gap-2 px-4 py-2 border border-gray-300 rounded-md hover:bg-gray-50"
          >
            <Download size={20} />
            تصدير
          </button>
        </div>
      </div>

      {/* Outputs Table */}
      <div className="bg-white rounded-lg shadow-sm overflow-hidden">
        <div className="overflow-x-auto">
          <table className="min-w-full divide-y divide-gray-200">
            <thead className="bg-gray-50">
              <tr>
                <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase">رقم الإخراج</th>
                <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase">التاريخ</th>
                <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase">المخزن</th>
                <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase">الجهة الطالبة</th>
                <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase">القسم</th>
                <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase">الحالة</th>
                <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase">إجراءات</th>
              </tr>
            </thead>
            <tbody className="bg-white divide-y divide-gray-200">
              {outputs.map((output) => (
                <tr key={output.id} className="hover:bg-gray-50">
                  <td className="px-6 py-4 whitespace-nowrap text-right">{output.reference}</td>
                  <td className="px-6 py-4 whitespace-nowrap text-right">
                    {new Date(output.date).toLocaleDateString('ar-EG')}
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-right">{output.warehouse}</td>
                  <td className="px-6 py-4 whitespace-nowrap text-right">{output.requestedBy}</td>
                  <td className="px-6 py-4 whitespace-nowrap text-right">{output.department}</td>
                  <td className="px-6 py-4 whitespace-nowrap text-right">
                    <span className={`px-2 py-1 rounded-full text-xs ${getStatusColor(output.status)}`}>
                      {output.status}
                    </span>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-right text-sm">
                    <button
                      onClick={() => setSelectedOutput(output)}
                      className="text-blue-600 hover:text-blue-900"
                    >
                      <Eye size={18} />
                    </button>
                  </td>
                </tr>
              ))}
            </tbody>
          </table>
        </div>
      </div>

      {/* View Output Modal */}
      {selectedOutput && (
        <div className="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full">
          <div className="relative top-20 mx-auto p-5 border w-3/4 shadow-lg rounded-md bg-white">
            <div className="flex justify-between items-start">
              <button
                onClick={() => setSelectedOutput(null)}
                className="text-gray-500 hover:text-gray-700"
              >
                ×
              </button>
              <h3 className="text-lg font-medium text-gray-900">تفاصيل الإخراج {selectedOutput.reference}</h3>
            </div>

            <div className="mt-4">
              <div className="grid grid-cols-2 gap-4 mb-4">
                <div>
                  <p className="text-sm text-gray-600">التاريخ</p>
                  <p className="font-medium">{new Date(selectedOutput.date).toLocaleDateString('ar-EG')}</p>
                </div>
                <div>
                  <p className="text-sm text-gray-600">المخزن</p>
                  <p className="font-medium">{selectedOutput.warehouse}</p>
                </div>
                <div>
                  <p className="text-sm text-gray-600">الجهة الطالبة</p>
                  <p className="font-medium">{selectedOutput.requestedBy}</p>
                </div>
                <div>
                  <p className="text-sm text-gray-600">منشئ الإخراج</p>
                  <p className="font-medium">{selectedOutput.createdBy}</p>
                </div>
              </div>

              <div className="mt-6">
                <h4 className="text-md font-medium mb-2">المواد المصروفة</h4>
                <table className="min-w-full divide-y divide-gray-200">
                  <thead className="bg-gray-50">
                    <tr>
                      <th className="px-4 py-2 text-right text-xs font-medium text-gray-500">المادة</th>
                      <th className="px-4 py-2 text-right text-xs font-medium text-gray-500">الكمية</th>
                      <th className="px-4 py-2 text-right text-xs font-medium text-gray-500">الوحدة</th>
                    </tr>
                  </thead>
                  <tbody>
                    {selectedOutput.items.map((item, index) => (
                      <tr key={index} className="hover:bg-gray-50">
                        <td className="px-4 py-2">{item.material}</td>
                        <td className="px-4 py-2">{item.quantity}</td>
                        <td className="px-4 py-2">{item.unit}</td>
                      </tr>
                    ))}
                  </tbody>
                </table>
              </div>

              <div className="mt-6 text-sm text-gray-600">
                ملاحظات: {selectedOutput.notes}
              </div>

              <div className="mt-6 flex justify-end gap-2">
                <button
                  onClick={() => setSelectedOutput(null)}
                  className="px-4 py-2 bg-gray-100 text-gray-700 rounded-md hover:bg-gray-200"
                >
                  إغلاق
                </button>
                <button
                  onClick={() => {}} // TODO: Implement print
                  className="flex items-center gap-2 px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700"
                >
                  <FileText size={18} />
                  طباعة إذن الصرف
                </button>
              </div>
            </div>
          </div>
        </div>
      )}
    </div>
  );

  // إرجاع المحتوى ملفوفاً في تخطيط لوحة التحكم
  return <DashboardLayout>{content}</DashboardLayout>;
}